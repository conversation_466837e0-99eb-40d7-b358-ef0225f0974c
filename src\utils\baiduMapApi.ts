// 百度地图API工具函数
export interface BaiduMapApiConfig {
  ak: string // 百度地图API密钥
  width?: number // 图片宽度，默认400
  height?: number // 图片高度，默认300
  zoom?: number // 缩放级别，默认15
}

// 默认配置
const defaultConfig: Required<Omit<BaiduMapApiConfig, 'ak'>> = {
  width: 400,
  height: 300,
  zoom: 15
}

/**
 * 生成百度静态地图图片URL
 * @param lat 纬度
 * @param lng 经度
 * @param config 配置参数
 * @returns 静态地图图片URL
 */
export function generateBaiduStaticMapUrl(
  lat: number,
  lng: number,
  config: BaiduMapApiConfig
): string {
  const { ak, width = defaultConfig.width, height = defaultConfig.height, zoom = defaultConfig.zoom } = config
  
  // 百度静态地图API URL
  const baseUrl = 'https://api.map.baidu.com/staticimage/v2'
  
  // 构建参数
  const params = new URLSearchParams({
    ak,
    center: `${lng},${lat}`, // 百度地图使用经度在前，纬度在后
    width: width.toString(),
    height: height.toString(),
    zoom: zoom.toString(),
    copyright: '1', // 显示版权信息
    dpiType: 'ph', // 高清图片
    // 添加标记点
    markers: `${lng},${lat}`,
    markerStyles: 'l,A,0xff0000,0xffff00,1' // 大号红色标记
  })
  
  return `${baseUrl}?${params.toString()}`
}

/**
 * 生成基地位置的静态地图图片URL
 * @param lat 纬度
 * @param lng 经度
 * @param config 配置参数
 * @returns 静态地图图片URL
 */
export function generateBaseMapImageUrl(
  lat: number,
  lng: number,
  config: BaiduMapApiConfig
): string {
  return generateBaiduStaticMapUrl(lat, lng, {
    ...config,
    zoom: 16 // 基地地图使用更高的缩放级别以显示更多细节
  })
}

/**
 * 批量生成所有基地的地图图片URL
 * @param bases 基地数据数组
 * @param config 配置参数
 * @returns 更新后的基地数据数组
 */
export function generateAllBaseMapImages<T extends { name: string; coordinates: { lat: number; lng: number } }>(
  bases: T[],
  config: BaiduMapApiConfig
): (T & { imageUrl: string })[] {
  return bases.map(base => ({
    ...base,
    imageUrl: generateBaseMapImageUrl(
      base.coordinates.lat,
      base.coordinates.lng,
      config
    )
  }))
}

// 错误处理：当API调用失败时使用的默认图片
export const DEFAULT_BASE_IMAGE = 'https://images.pexels.com/photos/1770809/pexels-photo-1770809.jpeg?auto=compress&cs=tinysrgb&w=400&h=300'

/**
 * 安全的图片URL生成函数，包含错误处理
 * @param lat 纬度
 * @param lng 经度
 * @param config 配置参数
 * @returns 静态地图图片URL或默认图片URL
 */
export function safeGenerateBaiduStaticMapUrl(
  lat: number,
  lng: number,
  config: BaiduMapApiConfig
): string {
  try {
    // 验证坐标有效性
    if (!lat || !lng || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      console.warn('无效的坐标参数:', { lat, lng })
      return DEFAULT_BASE_IMAGE
    }
    
    // 验证API密钥
    if (!config.ak) {
      console.warn('缺少百度地图API密钥')
      return DEFAULT_BASE_IMAGE
    }
    
    return generateBaiduStaticMapUrl(lat, lng, config)
  } catch (error) {
    console.error('生成百度地图图片URL失败:', error)
    return DEFAULT_BASE_IMAGE
  }
} 