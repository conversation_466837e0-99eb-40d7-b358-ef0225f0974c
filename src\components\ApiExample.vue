<template>
  <div class="api-example">
    <h2>API使用示例</h2>
    
    <!-- 分类列表 -->
    <div class="section">
      <h3>分类列表</h3>
      <el-button @click="loadCategories" :loading="loading.categories">
        加载分类列表
      </el-button>
      <div v-if="categories.length > 0" class="data-list">
        <div v-for="category in categories" :key="category.id" class="data-item">
          <span class="color-dot" :style="{ backgroundColor: category.color }"></span>
          <span>{{ category.title }}</span>
        </div>
      </div>
    </div>

    <!-- 基地列表 -->
    <div class="section">
      <h3>基地列表</h3>
      <el-button @click="loadBases" :loading="loading.bases">
        加载基地列表
      </el-button>
      <div v-if="bases.length > 0" class="data-list">
        <div v-for="base in bases" :key="base.id" class="data-item">
          <strong>{{ base.baseName }}</strong>
          <span>{{ base.city }} - {{ base.address }}</span>
        </div>
      </div>
    </div>

    <!-- 区划列表 -->
    <div class="section">
      <h3>区划列表</h3>
      <el-button @click="loadAreas" :loading="loading.areas">
        加载区划列表
      </el-button>
      <div v-if="areas.length > 0" class="data-list">
        <div v-for="area in areas" :key="area.cityCode" class="data-item">
          <span>{{ area.city }} ({{ area.cityCode }})</span>
        </div>
      </div>
    </div>

    <!-- 搜索示例 -->
    <div class="section">
      <h3>搜索基地</h3>
      <el-input 
        v-model="searchKeyword" 
        placeholder="输入搜索关键词"
        @keyup.enter="searchBases"
        style="width: 200px; margin-right: 10px;"
      />
      <el-button @click="searchBases" :loading="loading.search">
        搜索
      </el-button>
      <div v-if="searchResults.length > 0" class="data-list">
        <div v-for="base in searchResults" :key="base.id" class="data-item">
          <strong>{{ base.baseName }}</strong>
          <span>{{ base.city }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import ApiService from '../api/services'
import type { CategoryItem, BaseItem, AreaItem } from '../api'

// 响应式数据
const categories = ref<CategoryItem[]>([])
const bases = ref<BaseItem[]>([])
const areas = ref<AreaItem[]>([])
const searchResults = ref<BaseItem[]>([])
const searchKeyword = ref('')

// 加载状态
const loading = reactive({
  categories: false,
  bases: false,
  areas: false,
  search: false
})

// 加载分类列表
const loadCategories = async () => {
  loading.categories = true
  try {
    categories.value = await ApiService.fetchCategories()
    ElMessage.success(`成功加载 ${categories.value.length} 个分类`)
  } catch (error) {
    ElMessage.error('加载分类列表失败')
  } finally {
    loading.categories = false
  }
}

// 加载基地列表
const loadBases = async () => {
  loading.bases = true
  try {
    bases.value = await ApiService.fetchBases()
    ElMessage.success(`成功加载 ${bases.value.length} 个基地`)
  } catch (error) {
    ElMessage.error('加载基地列表失败')
  } finally {
    loading.bases = false
  }
}

// 加载区划列表
const loadAreas = async () => {
  loading.areas = true
  try {
    areas.value = await ApiService.fetchAreas()
    ElMessage.success(`成功加载 ${areas.value.length} 个区划`)
  } catch (error) {
    ElMessage.error('加载区划列表失败')
  } finally {
    loading.areas = false
  }
}

// 搜索基地
const searchBases = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  loading.search = true
  try {
    searchResults.value = await ApiService.searchBases(searchKeyword.value)
    ElMessage.success(`找到 ${searchResults.value.length} 个相关基地`)
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    loading.search = false
  }
}
</script>

<style scoped>
.api-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafbfc;
}

.section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.data-list {
  margin-top: 15px;
}

.data-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.data-item strong {
  color: #303133;
  font-weight: 600;
}

.data-item span {
  color: #606266;
  font-size: 14px;
}
</style>
