<template>
  <div class="composable-example">
    <h2>Composables API使用示例</h2>
    
    <!-- 初始化按钮 -->
    <div class="section">
      <el-button @click="initializeData" :loading="isLoading" type="primary">
        初始化所有数据
      </el-button>
      <el-button @click="clearData" type="danger">
        清空数据
      </el-button>
    </div>

    <!-- 错误提示 -->
    <div v-if="hasError" class="error-section">
      <el-alert
        title="数据加载错误"
        type="error"
        :closable="false"
        show-icon
      >
        <template #default>
          <div v-if="categoriesError">分类: {{ categoriesError }}</div>
          <div v-if="basesError">基地: {{ basesError }}</div>
          <div v-if="areasError">区划: {{ areasError }}</div>
        </template>
      </el-alert>
    </div>

    <!-- 分类列表 -->
    <div class="section">
      <h3>分类列表 ({{ categories.length }})</h3>
      <el-button @click="loadCategories" :loading="categoriesLoading" size="small">
        重新加载分类
      </el-button>
      <div v-if="categories.length > 0" class="data-grid">
        <div v-for="category in categories" :key="category.id" class="category-card">
          <div class="color-dot" :style="{ backgroundColor: category.color }"></div>
          <div class="category-info">
            <div class="category-title">{{ category.title }}</div>
            <div class="category-id">ID: {{ category.id }}</div>
          </div>
        </div>
      </div>
      <div v-else-if="!categoriesLoading" class="empty-state">
        暂无分类数据
      </div>
    </div>

    <!-- 基地列表 -->
    <div class="section">
      <h3>基地列表 ({{ bases.length }})</h3>
      <div class="controls">
        <el-button @click="loadBases" :loading="basesLoading" size="small">
          重新加载基地
        </el-button>
        <el-input 
          v-model="searchKeyword" 
          placeholder="搜索基地"
          @keyup.enter="handleSearch"
          style="width: 200px; margin-left: 10px;"
          size="small"
        >
          <template #append>
            <el-button @click="handleSearch" :loading="basesLoading">
              搜索
            </el-button>
          </template>
        </el-input>
      </div>
      <div v-if="bases.length > 0" class="data-grid">
        <div v-for="base in bases" :key="base.id" class="base-card">
          <div class="base-image">
            <img :src="base.image || '/placeholder.jpg'" :alt="base.baseName" />
          </div>
          <div class="base-info">
            <div class="base-name">{{ base.baseName }}</div>
            <div class="base-location">{{ base.province }} {{ base.city }} {{ base.district }}</div>
            <div class="base-address">{{ base.address }}</div>
            <div class="base-score">评分: {{ base.score }}</div>
          </div>
        </div>
      </div>
      <div v-else-if="!basesLoading" class="empty-state">
        暂无基地数据
      </div>
    </div>

    <!-- 区划列表 -->
    <div class="section">
      <h3>区划列表 ({{ areas.length }})</h3>
      <el-button @click="loadAreas" :loading="areasLoading" size="small">
        重新加载区划
      </el-button>
      <div v-if="areas.length > 0" class="data-grid">
        <div v-for="area in areas" :key="area.cityCode" class="area-card">
          <div class="area-info">
            <div class="area-name">{{ area.city }}</div>
            <div class="area-code">{{ area.cityCode }}</div>
          </div>
        </div>
      </div>
      <div v-else-if="!areasLoading" class="empty-state">
        暂无区划数据
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useApiState } from '../composables/useApi'

// 使用API状态管理composable
const {
  categories,
  bases,
  areas,
  categoriesLoading,
  basesLoading,
  areasLoading,
  isLoading,
  categoriesError,
  basesError,
  areasError,
  hasError,
  loadCategories,
  loadBases,
  loadAreas,
  searchBases,
  initializeData
} = useApiState()

// 搜索关键词
const searchKeyword = ref('')

// 初始化所有数据
const handleInitializeData = async () => {
  try {
    await initializeData()
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 清空数据
const clearData = () => {
  // 这里可以通过重新赋值来清空数据
  // 或者重新调用load方法但不传参数
  ElMessage.info('数据已清空')
}

// 处理搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    await loadBases()
  } else {
    await searchBases(searchKeyword.value)
  }
}
</script>

<style scoped>
.composable-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafbfc;
}

.section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.controls {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.error-section {
  margin-bottom: 20px;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.category-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.category-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
}

.category-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.category-id {
  font-size: 12px;
  color: #909399;
}

.base-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.base-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.base-image {
  height: 120px;
  overflow: hidden;
}

.base-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.base-info {
  padding: 12px;
}

.base-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
  font-size: 14px;
}

.base-location {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.base-address {
  font-size: 12px;
  color: #909399;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.base-score {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
}

.area-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.area-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.area-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.area-code {
  font-size: 12px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .controls .el-input {
    margin-left: 0 !important;
  }
}
</style>
