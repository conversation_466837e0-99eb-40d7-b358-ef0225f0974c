<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { mockBases } from '../data/mockData'
import type { PracticeBase } from '../types'

// 接收路由参数
const props = defineProps<{
  baseId?: string
}>()

// 当前编辑的基地信息
const currentBase = ref<PracticeBase | null>(null)

// 组件类型定义
interface CanvasComponent {
  id: string
  name: string
  icon: string
  type: string
  x: number
  y: number
  width: number
  height: number
}
import { 
  ArrowLeft, 
  View, 
  DocumentCopy, 
  Refresh,
  Monitor,
  Iphone,
  Download,
  Upload,
  Delete,
  Edit
} from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 编辑器状态
const editorState = reactive({
  selectedComponent: null as any,
  previewMode: false,
  deviceType: 'desktop' as 'desktop' | 'tablet' | 'mobile',
  showGrid: true,
  zoom: 100
})

// 可用组件库
const componentLibrary = ref([
  {
    category: '布局组件',
    components: [
      { id: 'container', name: '容器', icon: 'material-symbols:view-module', description: '基础容器组件' },
      { id: 'row', name: '行', icon: 'material-symbols:view-stream', description: '水平布局行' },
      { id: 'column', name: '列', icon: 'material-symbols:view-column', description: '垂直布局列' },
      { id: 'grid', name: '网格', icon: 'material-symbols:grid-view', description: '网格布局' }
    ]
  },
  {
    category: '内容组件',
    components: [
      { id: 'text', name: '文本', icon: 'material-symbols:text-fields', description: '文本内容' },
      { id: 'title', name: '标题', icon: 'material-symbols:title', description: '标题组件' },
      { id: 'image', name: '图片', icon: 'material-symbols:image', description: '图片展示' },
      { id: 'video', name: '视频', icon: 'material-symbols:video-library', description: '视频播放器' }
    ]
  },
  {
    category: '交互组件',
    components: [
      { id: 'button', name: '按钮', icon: 'material-symbols:smart-button', description: '交互按钮' },
      { id: 'form', name: '表单', icon: 'material-symbols:edit-document', description: '表单组件' },
      { id: 'input', name: '输入框', icon: 'material-symbols:input', description: '文本输入' },
      { id: 'select', name: '选择器', icon: 'material-symbols:arrow-drop-down', description: '下拉选择' }
    ]
  },
  {
    category: '展示组件',
    components: [
      { id: 'card', name: '卡片', icon: 'material-symbols:credit-card', description: '信息卡片' },
      { id: 'list', name: '列表', icon: 'material-symbols:list', description: '列表展示' },
      { id: 'table', name: '表格', icon: 'material-symbols:table', description: '数据表格' },
      { id: 'chart', name: '图表', icon: 'material-symbols:bar-chart', description: '数据图表' }
    ]
  }
])

// 画布上的组件
const canvasComponents = ref<CanvasComponent[]>([])

// 组件属性面板数据
const componentProperties = ref({
  style: {
    width: '100%',
    height: 'auto',
    margin: '0px',
    padding: '16px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #e4e7ed'
  },
  content: {
    text: '示例文本',
    placeholder: '请输入内容',
    src: '',
    alt: ''
  },
  layout: {
    display: 'block',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    gap: '8px'
  }
})

// 属性面板折叠状态
const activeCollapse = ref(['style'])

// 工具栏操作
const handleSave = () => {
  ElMessage.success('页面配置已保存')
}

const handlePreview = () => {
  editorState.previewMode = !editorState.previewMode
  ElMessage.info(editorState.previewMode ? '已切换到预览模式' : '已切换到编辑模式')
}

const handleExport = () => {
  ElMessage.success('页面配置已导出')
}

const handleImport = () => {
  ElMessage.success('页面配置已导入')
}

const handleDeviceChange = (device: string) => {
  editorState.deviceType = device as any
}

const handleZoomChange = (zoom: number) => {
  editorState.zoom = zoom
}

// 组件操作
const handleComponentDrag = (component: any) => {
  console.log('开始拖拽组件:', component)
}

const handleComponentDrop = () => {
  console.log('组件放置到画布')
}

const handleComponentSelect = (component: any) => {
  editorState.selectedComponent = component
}

const handleComponentDelete = (component: any) => {
  const index = canvasComponents.value.findIndex(c => c.id === component.id)
  if (index > -1) {
    canvasComponents.value.splice(index, 1)
  }
}

const handleGoBack = () => {
  router.back()
}

// 初始化函数
const initializeEditor = () => {
  if (props.baseId) {
    currentBase.value = mockBases.find(base => base.id === props.baseId) || null
    if (currentBase.value) {
      console.log('正在为基地配置页面:', currentBase.value.name)
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  initializeEditor()
})

// 计算样式
const canvasStyle = computed(() => ({
  zoom: `${editorState.zoom}%`,
  maxWidth: editorState.deviceType === 'mobile' ? '375px' : 
            editorState.deviceType === 'tablet' ? '768px' : '100%'
}))
</script>

<template>
  <div class="lowcode-editor">
    <!-- 顶部工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button @click="handleGoBack" :icon="ArrowLeft" text>
          返回管理
        </el-button>
        <div class="page-info">
          <h3>
            低代码页面编辑器
            <span v-if="currentBase" class="base-name">- {{ currentBase.name }}</span>
          </h3>
          <span class="page-subtitle">
            {{ currentBase ? `为 "${currentBase.name}" 配置详情页面` : '可视化页面配置工具' }}
          </span>
        </div>
      </div>
      
      <div class="toolbar-center">
        <!-- 设备切换 -->
        <div class="device-switcher">
          <el-tooltip content="桌面端预览" placement="bottom">
            <el-button 
              :type="editorState.deviceType === 'desktop' ? 'primary' : ''"
              :icon="Monitor"
              @click="handleDeviceChange('desktop')"
              size="small" />
          </el-tooltip>
          <el-tooltip content="平板预览" placement="bottom">
            <el-button 
              :type="editorState.deviceType === 'tablet' ? 'primary' : ''"
              @click="handleDeviceChange('tablet')"
              size="small">
              <Icon icon="material-symbols:tablet" style="width: 16px; height: 16px;" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="移动端预览" placement="bottom">
            <el-button 
              :type="editorState.deviceType === 'mobile' ? 'primary' : ''"
              :icon="Iphone"
              @click="handleDeviceChange('mobile')"
              size="small" />
          </el-tooltip>
        </div>
        
        <!-- 缩放控制 -->
        <div class="zoom-control">
          <el-button @click="handleZoomChange(editorState.zoom - 10)" size="small" text>
            <Icon icon="material-symbols:zoom-out" style="width: 16px; height: 16px;" />
          </el-button>
          <span class="zoom-value">{{ editorState.zoom }}%</span>
          <el-button @click="handleZoomChange(editorState.zoom + 10)" size="small" text>
            <Icon icon="material-symbols:zoom-in" style="width: 16px; height: 16px;" />
          </el-button>
        </div>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="handleImport" :icon="Upload" size="small" text>
          导入
        </el-button>
        <el-button @click="handleExport" :icon="Download" size="small" text>
          导出
        </el-button>
        <el-button @click="handlePreview" :icon="View" size="small" type="primary" plain>
          {{ editorState.previewMode ? '编辑' : '预览' }}
        </el-button>
        <el-button @click="handleSave" :icon="DocumentCopy" size="small" type="primary">
          保存
        </el-button>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-body">
      <!-- 左侧组件库 -->
      <div class="component-library">
        <div class="library-header">
          <h4>
            <Icon icon="material-symbols:widgets" style="width: 18px; height: 18px;" />
            组件库
          </h4>
        </div>
        
        <div class="library-content">
          <div v-for="category in componentLibrary" :key="category.category" class="component-category">
            <div class="category-title">
              {{ category.category }}
            </div>
            
            <div class="component-grid">
              <div 
                v-for="component in category.components" 
                :key="component.id"
                class="component-item"
                :draggable="!editorState.previewMode"
                @dragstart="handleComponentDrag(component)"
                :title="component.description"
              >
                <div class="component-icon">
                  <Icon :icon="component.icon" style="width: 24px; height: 24px;" />
                </div>
                <div class="component-name">{{ component.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-area">
        <div class="canvas-header">
          <div class="canvas-title">
            <Icon icon="material-symbols:dashboard" style="width: 18px; height: 18px;" />
            设计画布
          </div>
          <div class="canvas-actions">
            <el-button 
              :type="editorState.showGrid ? 'primary' : ''"
              @click="editorState.showGrid = !editorState.showGrid"
              size="small" 
              text>
              <Icon icon="material-symbols:grid-on" style="width: 16px; height: 16px;" />
              网格
            </el-button>
            <el-button @click="canvasComponents = []" :icon="Refresh" size="small" text>
              清空
            </el-button>
          </div>
        </div>
        
        <div class="canvas-container">
          <div 
            class="canvas"
            :class="{ 
              'show-grid': editorState.showGrid,
              'preview-mode': editorState.previewMode,
              [`device-${editorState.deviceType}`]: true
            }"
            :style="canvasStyle"
            @drop="handleComponentDrop"
            @dragover.prevent
          >
            <!-- 空状态提示 -->
            <div v-if="canvasComponents.length === 0" class="canvas-empty">
              <div class="empty-content">
                <Icon icon="material-symbols:drag-indicator" style="width: 48px; height: 48px; color: #c0c4cc;" />
                <h4>从左侧组件库拖拽组件到此处开始设计</h4>
                <p>支持拖拽、编辑、预览等功能</p>
              </div>
            </div>
            
            <!-- 画布组件渲染区域 -->
            <div v-else class="canvas-components">
                            <div 
                v-for="component in canvasComponents"
                :key="component.id"
                class="canvas-component"
                :class="{ 'selected': editorState.selectedComponent?.id === component.id }"
                @click="handleComponentSelect(component)"
              >
                <!-- 组件内容占位 -->
                <div class="component-placeholder">
                  <Icon :icon="component.icon" style="width: 32px; height: 32px;" />
                  <span>{{ component.name }}</span>
                </div>
                
                <!-- 组件操作按钮 -->
                <div v-if="!editorState.previewMode" class="component-actions">
                  <el-button :icon="Edit" size="small" circle />
                  <el-button :icon="Delete" size="small" circle type="danger" @click.stop="handleComponentDelete(component)" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h4>
            <Icon icon="material-symbols:tune" style="width: 18px; height: 18px;" />
            属性配置
          </h4>
        </div>
        
        <div class="panel-content">
          <div v-if="!editorState.selectedComponent" class="no-selection">
            <Icon icon="material-symbols:touch-app" style="width: 48px; height: 48px; color: #c0c4cc;" />
            <p>请选择一个组件来编辑其属性</p>
          </div>
          
          <div v-else class="property-groups">
            <!-- 样式属性 -->
            <el-collapse v-model="activeCollapse" accordion>
              <el-collapse-item title="样式设置" name="style">
                <div class="property-group">
                  <div class="property-item">
                    <label>宽度</label>
                    <el-input v-model="componentProperties.style.width" size="small" />
                  </div>
                  <div class="property-item">
                    <label>高度</label>
                    <el-input v-model="componentProperties.style.height" size="small" />
                  </div>
                  <div class="property-item">
                    <label>内边距</label>
                    <el-input v-model="componentProperties.style.padding" size="small" />
                  </div>
                  <div class="property-item">
                    <label>外边距</label>
                    <el-input v-model="componentProperties.style.margin" size="small" />
                  </div>
                  <div class="property-item">
                    <label>背景色</label>
                    <el-color-picker v-model="componentProperties.style.backgroundColor" size="small" />
                  </div>
                  <div class="property-item">
                    <label>圆角</label>
                    <el-input v-model="componentProperties.style.borderRadius" size="small" />
                  </div>
                </div>
              </el-collapse-item>
              
              <!-- 内容属性 -->
              <el-collapse-item title="内容设置" name="content">
                <div class="property-group">
                  <div class="property-item">
                    <label>文本内容</label>
                    <el-input v-model="componentProperties.content.text" type="textarea" size="small" />
                  </div>
                  <div class="property-item">
                    <label>占位符</label>
                    <el-input v-model="componentProperties.content.placeholder" size="small" />
                  </div>
                </div>
              </el-collapse-item>
              
              <!-- 布局属性 -->
              <el-collapse-item title="布局设置" name="layout">
                <div class="property-group">
                  <div class="property-item">
                    <label>显示方式</label>
                    <el-select v-model="componentProperties.layout.display" size="small">
                      <el-option label="块级" value="block" />
                      <el-option label="行内块" value="inline-block" />
                      <el-option label="弹性布局" value="flex" />
                      <el-option label="网格布局" value="grid" />
                    </el-select>
                  </div>
                  <div class="property-item">
                    <label>对齐方式</label>
                    <el-select v-model="componentProperties.layout.justifyContent" size="small">
                      <el-option label="左对齐" value="flex-start" />
                      <el-option label="居中" value="center" />
                      <el-option label="右对齐" value="flex-end" />
                      <el-option label="两端对齐" value="space-between" />
                    </el-select>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.lowcode-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  overflow: hidden;
}

/* 顶部工具栏 */
.editor-toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-info h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.page-subtitle {
  font-size: 12px;
  color: #909399;
}

.base-name {
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 24px;
}

.device-switcher {
  display: flex;
  gap: 4px;
  padding: 4px;
  background: #f5f7fa;
  border-radius: 6px;
}

.zoom-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.zoom-value {
  font-size: 12px;
  color: #606266;
  min-width: 40px;
  text-align: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 编辑器主体 */
.editor-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧组件库 */
.component-library {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.library-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
}

.library-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.component-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 12px;
  color: #909399;
  font-weight: 600;
  margin-bottom: 8px;
  padding: 0 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.component-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.component-item:hover::before {
  left: 100%;
}

.component-item:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f3ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0) scale(0.98);
}

.component-icon {
  margin-bottom: 6px;
  color: #606266;
  transition: color 0.3s ease;
}

.component-item:hover .component-icon {
  color: #409eff;
}

.component-name {
  font-size: 12px;
  color: #303133;
  text-align: center;
  font-weight: 500;
}

/* 中间画布区域 */
.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fafbfc;
}

.canvas-header {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.canvas-title {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.canvas-actions {
  display: flex;
  gap: 8px;
}

.canvas-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.canvas {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  min-height: 600px;
  width: 100%;
  max-width: 1200px;
  position: relative;
  transition: all 0.3s ease;
}

.canvas.show-grid {
  background-image: 
    linear-gradient(rgba(64, 158, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(64, 158, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.canvas.device-mobile {
  max-width: 375px;
  min-height: 667px;
}

.canvas.device-tablet {
  max-width: 768px;
  min-height: 1024px;
}

.canvas.preview-mode {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.canvas-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  color: #909399;
}

.empty-content h4 {
  margin: 16px 0 8px 0;
  color: #606266;
  font-weight: 500;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.canvas-components {
  padding: 20px;
  min-height: 100%;
}

.canvas-component {
  position: relative;
  margin-bottom: 16px;
  border: 2px dashed transparent;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.canvas-component:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.canvas-component.selected {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.component-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  background: #fafbfc;
  border-radius: 4px;
  border: 1px dashed #e4e7ed;
}

.component-placeholder span {
  margin-top: 8px;
  font-size: 14px;
}

.component-actions {
  position: absolute;
  top: -16px;
  right: -16px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.canvas-component:hover .component-actions,
.canvas-component.selected .component-actions {
  opacity: 1;
}

/* 右侧属性面板 */
.properties-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background: #fafbfc;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.no-selection {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.no-selection p {
  margin: 16px 0 0 0;
  font-size: 14px;
}

.property-groups {
  width: 100%;
}

.property-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.property-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.property-item label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .component-library {
    width: 240px;
  }
  
  .properties-panel {
    width: 280px;
  }
}

/* 滚动条样式 */
.library-content::-webkit-scrollbar,
.canvas-container::-webkit-scrollbar,
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.library-content::-webkit-scrollbar-track,
.canvas-container::-webkit-scrollbar-track,
.panel-content::-webkit-scrollbar-track {
  background: #f1f3f4;
}

.library-content::-webkit-scrollbar-thumb,
.canvas-container::-webkit-scrollbar-thumb,
.panel-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.library-content::-webkit-scrollbar-thumb:hover,
.canvas-container::-webkit-scrollbar-thumb:hover,
.panel-content::-webkit-scrollbar-thumb:hover {
  background: #909399;
}
</style> 