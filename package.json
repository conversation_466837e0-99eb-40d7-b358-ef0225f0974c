{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/icons-material-symbols": "^1.2.58", "@iconify/icons-mdi": "^1.2.48", "@iconify/icons-ri": "^1.2.10", "@iconify/vue": "^5.0.0", "@tailwindcss/postcss": "^4.1.11", "axios": "^1.11.0", "element-plus": "^2.10.3", "vue": "^3.4.38", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}