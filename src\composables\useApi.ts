import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import ApiService from '../api/services'
import type { CategoryItem, BaseItem, AreaItem, CategoryListParams, BaseListParams } from '../api'

/**
 * 分类相关的composable
 */
export function useCategories() {
  const categories = ref<CategoryItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadCategories = async (params?: CategoryListParams) => {
    loading.value = true
    error.value = null
    try {
      categories.value = await ApiService.fetchCategories(params)
    } catch (err) {
      error.value = '加载分类列表失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    categories: computed(() => categories.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    loadCategories
  }
}

/**
 * 基地相关的composable
 */
export function useBases() {
  const bases = ref<BaseItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadBases = async (params?: BaseListParams) => {
    loading.value = true
    error.value = null
    try {
      bases.value = await ApiService.fetchBases(params)
    } catch (err) {
      error.value = '加载基地列表失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const searchBases = async (keyword: string) => {
    loading.value = true
    error.value = null
    try {
      bases.value = await ApiService.searchBases(keyword)
    } catch (err) {
      error.value = '搜索基地失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const loadBasesByCategory = async (categoryId: number) => {
    loading.value = true
    error.value = null
    try {
      bases.value = await ApiService.fetchBasesByCategory(categoryId)
    } catch (err) {
      error.value = '根据分类加载基地失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const loadBasesByArea = async (areaCode: string) => {
    loading.value = true
    error.value = null
    try {
      bases.value = await ApiService.fetchBasesByArea(areaCode)
    } catch (err) {
      error.value = '根据区域加载基地失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    bases: computed(() => bases.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    loadBases,
    searchBases,
    loadBasesByCategory,
    loadBasesByArea
  }
}

/**
 * 区划相关的composable
 */
export function useAreas() {
  const areas = ref<AreaItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadAreas = async () => {
    loading.value = true
    error.value = null
    try {
      areas.value = await ApiService.fetchAreas()
    } catch (err) {
      error.value = '加载区划列表失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    areas: computed(() => areas.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    loadAreas
  }
}

/**
 * 综合API状态管理composable
 */
export function useApiState() {
  const { categories, loading: categoriesLoading, error: categoriesError, loadCategories } = useCategories()
  const { bases, loading: basesLoading, error: basesError, loadBases, searchBases } = useBases()
  const { areas, loading: areasLoading, error: areasError, loadAreas } = useAreas()

  // 综合加载状态
  const isLoading = computed(() => 
    categoriesLoading.value || basesLoading.value || areasLoading.value
  )

  // 综合错误状态
  const hasError = computed(() => 
    categoriesError.value || basesError.value || areasError.value
  )

  // 初始化所有数据
  const initializeData = async () => {
    try {
      await Promise.all([
        loadCategories(),
        loadBases(),
        loadAreas()
      ])
      ElMessage.success('数据初始化成功')
    } catch (error) {
      ElMessage.error('数据初始化失败')
    }
  }

  return {
    // 数据
    categories,
    bases,
    areas,
    
    // 加载状态
    categoriesLoading,
    basesLoading,
    areasLoading,
    isLoading,
    
    // 错误状态
    categoriesError,
    basesError,
    areasError,
    hasError,
    
    // 方法
    loadCategories,
    loadBases,
    loadAreas,
    searchBases,
    initializeData
  }
}
