import { tiandituMapConfig } from '../config/tiandituMapConfig'

/**
 * 动态加载天地图JavaScript API
 * 使用环境变量中的API密钥，避免在静态HTML中硬编码
 */
export function preloadTiandituMapSDK(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载过天地图SDK
    if (document.getElementById('tianditu-api-script')) {
      console.log('✅ 天地图API已加载')
      resolve()
      return
    }

    // 获取天地图API密钥
    const tk = tiandituMapConfig.tk

    if (!tk) {
      reject(new Error('缺少天地图API密钥，请在环境变量中设置VITE_TIANDITU_MAP_TK'))
      return
    }

    // 创建加载天地图API的script标签
    const script = document.createElement('script')
    script.id = 'tianditu-api-script'
    script.type = 'text/javascript'
    script.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${tk}`
    script.async = true
    script.defer = true
    
    // 监听加载事件
    script.onload = () => {
      console.log('✅ 天地图API加载成功')
      resolve()
    }
    
    script.onerror = (error) => {
      console.error('❌ 天地图API加载失败', error)
      reject(new Error('天地图API加载失败'))
    }
    
    // 将script标签添加到页面
    document.head.appendChild(script)
    
    console.log('🔄 正在加载天地图API...')
  })
}

/**
 * 检查天地图API是否已加载
 * 用于需要使用天地图API的组件进行检查
 */
export function isTiandituMapLoaded(): boolean {
  return typeof window !== 'undefined' && !!(window as any).T
}

/**
 * 确保天地图API已加载
 * 如果未加载，则动态加载
 */
export async function ensureTiandituMapLoaded(): Promise<void> {
  if (isTiandituMapLoaded()) {
    return Promise.resolve()
  }
  
  return preloadTiandituMapSDK()
} 