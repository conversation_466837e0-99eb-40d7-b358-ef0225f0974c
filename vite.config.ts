import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/api': {
        target: 'http://120.55.15.172:8100',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, options) => {
          // 配置代理选项
          proxy.on('error', (err, req, res) => {
            // 代理错误处理
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 代理请求处理
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 代理响应处理
          })
        }
      }
    },
    // 开发服务器配置
    host: '0.0.0.0',
    port: 5174,
    cors: true
  }
})
