import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/api': {
        target: 'http://120.55.15.172:8100',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        secure: false, // 如果是https目标，设置为false忽略证书验证
        configure: (proxy) => {
          // 配置代理选项
          proxy.on('error', (err, req) => {
            console.error('代理错误:', err.message, req.url)
          })
          proxy.on('proxyReq', (proxyReq, req) => {
            console.log('代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req) => {
            console.log('代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    },
    // 开发服务器配置
    host: '0.0.0.0',
    port: 5174,
    cors: true
  }
})
