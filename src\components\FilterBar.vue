<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'

interface Props {
  totalCount: number
}

const props = defineProps<Props>()
const emit = defineEmits<{
  filterChange: [filters: { city: string; category: string; search: string }]
}>()

const searchQuery = ref('')
const selectedCity = ref('')
const selectedCategory = ref('')

const cities = [
  { value: '', label: '全部城市' },
  { value: '南昌市', label: '南昌市' },
  { value: '井冈山市', label: '井冈山市' },
  { value: '瑞金市', label: '瑞金市' },
  { value: '九江市', label: '九江市' },
  { value: '萍乡市', label: '萍乡市' },
  { value: '新余市', label: '新余市' },
  { value: '鹰潭市', label: '鹰潭市' },
  { value: '赣州市', label: '赣州市' },
  { value: '宜春市', label: '宜春市' },
  { value: '上饶市', label: '上饶市' },
  { value: '吉安市', label: '吉安市' },
  { value: '抚州市', label: '抚州市' }
]

const categories = [
  { value: '', label: '全部分类' },
  { value: '革命历史', label: '革命历史' },
  { value: '爱国主义', label: '爱国主义' },
  { value: '红色文化', label: '红色文化' },
  { value: '党史教育', label: '党史教育' },
  { value: '英烈纪念', label: '英烈纪念' }
]

const handleSearch = () => {
  emit('filterChange', {
    city: selectedCity.value,
    category: selectedCategory.value,
    search: searchQuery.value
  })
}

const handleReset = () => {
  searchQuery.value = ''
  selectedCity.value = ''
  selectedCategory.value = ''
  emit('filterChange', {
    city: '',
    category: '',
    search: ''
  })
}

const handleFilterChange = () => {
  emit('filterChange', {
    city: selectedCity.value,
    category: selectedCategory.value,
    search: searchQuery.value
  })
}
</script>

<template>
  <div class="filter-bar">
    <div class="container">
      <div class="filter-content">
        <div class="filter-left">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索基地名称或关键词"
              :prefix-icon="Search"
              @keyup.enter="handleSearch"
              @input="handleFilterChange"
              clearable
            />
          </div>
          
          <div class="filter-selects">
            <el-select
              v-model="selectedCity"
              placeholder="选择城市"
              @change="handleFilterChange"
              clearable
            >
              <el-option
                v-for="city in cities"
                :key="city.value"
                :label="city.label"
                :value="city.value"
              />
            </el-select>
            
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              @change="handleFilterChange"
              clearable
            >
              <el-option
                v-for="category in categories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              />
            </el-select>
          </div>
        </div>
        
        <div class="filter-right">
          <div class="result-count">
            <span>共找到 <strong>{{ props.totalCount }}</strong> 个基地</span>
          </div>
          
          <div class="filter-actions">
            <el-button @click="handleReset" :icon="Refresh">
              重置
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-bar {
  background: white;
  border-bottom: 1px solid #ebeef5;
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.search-box {
  min-width: 300px;
}

.filter-selects {
  display: flex;
  gap: 12px;
}

.filter-selects .el-select {
  width: 120px;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.result-count {
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
}

.result-count strong {
  color: #409eff;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .filter-content {
    flex-direction: column;
    gap: 16px;
  }

  .filter-left {
    width: 100%;
    flex-direction: column;
    gap: 12px;
  }

  .search-box {
    width: 100%;
    min-width: auto;
  }

  .filter-selects {
    width: 100%;
    justify-content: space-between;
  }

  .filter-selects .el-select {
    flex: 1;
  }

  .filter-right {
    width: 100%;
    justify-content: space-between;
  }
}
</style>