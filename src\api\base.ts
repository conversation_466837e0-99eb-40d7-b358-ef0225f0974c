import { request, ApiResponse } from './index'

// 基地列表请求参数接口
export interface BaseListParams {
  areaCode?: string
  areaName?: string
  title?: string
}

// 基地数据接口
export interface BaseItem {
  address: string
  baseDesc: string
  baseName: string
  categoryId: number
  city: string
  cityCode: string
  district: string
  districtCode: string
  id: number
  image: string
  lat: number
  lng: number
  openTime: string
  phone: string
  province: string
  provinceCode: string
  score: number
  title: string
}

// 基地列表响应接口
export interface BaseListResponse {
  code: string
  data: BaseItem[]
  msg: string
}

/**
 * 获取基地列表
 * @param params 请求参数
 * @returns Promise<BaseListResponse>
 */
export const getBaseList = (params?: BaseListParams): Promise<ApiResponse<BaseItem[]>> => {
  return request.post<BaseItem[]>('/base/public/baseInfoList', params)
}

// 导出所有基地相关的API
export const baseApi = {
  getBaseList
}
