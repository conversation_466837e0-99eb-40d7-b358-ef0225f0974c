<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineProps, defineEmits, nextTick, watch, computed } from 'vue'
import type { PracticeBase } from '../types'

// 处理导航功能的函数 - 使用百度地图导航
const handleNavigation = async (base: PracticeBase) => {
  try {
    console.log('🧭 开始导航到:', base.name);
    
    const el = document.getElementById(`nav-link-${base.id}`);
    if (el) {
      el.classList.add('navigating');
    }
    
    // 构建完整的地址
    const fullAddress = `${base.city}${base.address}`;
    console.log('🎯 目标地址:', fullAddress);
    
    // 检测设备类型
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    let navUrl = '';
    
    // 百度地图在处理POI搜索方面表现更好，且支持更完善的URI方案
    if (isMobile) {
      // 移动端使用百度地图APP
      navUrl = `bdapp://map/geocoder?address=${encodeURIComponent(fullAddress)}&src=andr.baidu.openAPIdemo`;
      
      // 添加iOS的通用链接支持
      if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
        navUrl = `baidumap://map/geocoder?address=${encodeURIComponent(fullAddress)}&src=ios.baidu.openAPIdemo`;
      }
    } else {
      // 桌面端使用百度地图网页版
      navUrl = `https://api.map.baidu.com/geocoder?address=${encodeURIComponent(fullAddress)}&output=html`;
    }
    
    console.log('🔗 导航URL:', navUrl);
    
    // 处理不同情况下的导航逻辑
    if (isMobile) {
      // 移动端尝试打开百度地图APP
      window.location.href = navUrl;
      
      // 设置一个延迟，如果APP没有打开，则使用网页版作为备用
      setTimeout(() => {
        // 检查页面是否仍然可见（如果APP打开成功，页面会失去焦点）
        if (!document.hidden) {
          console.log('百度地图APP可能未安装，使用网页版导航');
          const backupUrl = `https://map.baidu.com/search/${encodeURIComponent(base.name)}/${base.coordinates.lng},${base.coordinates.lat}`;
          window.location.href = backupUrl;
        }
      }, 2000);
    } else {
      // 桌面端直接打开新窗口
      const newWindow = window.open(navUrl, '_blank');
      
      // 如果弹窗被阻止，提示用户
      if (!newWindow) {
        alert('请允许弹窗以打开地图导航页面');
      }
    }
    
    // 恢复地址区域状态
    if (el) {
      setTimeout(() => {
        el.classList.remove('navigating');
      }, 1500);
    }
    
  } catch (error) {
    console.error('❌ 导航过程中出错:', error);
    
    // 降级方案：直接使用百度地图搜索
    const fallbackUrl = `https://map.baidu.com/search/${encodeURIComponent(base.name)}`;
    
    const el = document.getElementById(`nav-link-${base.id}`);
    if (el) {
      el.classList.remove('navigating');
      el.classList.add('nav-error');
      
      // 直接打开降级URL
      window.open(fallbackUrl, '_blank');
      
      // 清除错误状态
      setTimeout(() => {
        el.classList.remove('nav-error');
      }, 2000);
    }
  }
};

interface Props {
  bases: PracticeBase[]
  defaultCenter?: { lat: number, lng: number }
  defaultZoom?: number
  panelVisible?: boolean
  hideMapLegend?: boolean
  hideMapStats?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultCenter: () => ({ lat: 26.646412, lng: 114.160148 }),
  defaultZoom: 12,
  panelVisible: false,
  hideMapLegend: false,
  hideMapStats: false
})

const emit = defineEmits<{
  baseClick: [base: PracticeBase]
  baseLocate: [base: PracticeBase]
  viewDetails: [base: PracticeBase]
}>()

const mapContainer = ref<HTMLElement>()
const isMapReady = ref(false)
const activeLegendItems = ref<Set<string>>(new Set())
const highlightedMarkerId = ref<string | null>(null)
const isMobile = ref(false)

let map: any = null
let markers: any[] = []
let allMarkers: any[] = []

// 检测移动设备
const checkMobile = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = ['android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone', 'mobile']
  const isMobileUserAgent = mobileKeywords.some(keyword => userAgent.includes(keyword))
  const isSmallScreen = window.innerWidth <= 768
  
  isMobile.value = isMobileUserAgent || isSmallScreen
  console.log('🔍 设备检测:', { isMobileUserAgent, isSmallScreen, isMobile: isMobile.value })
}

// 获取基地类别列表
const categories = computed(() => {
  const categorySet = new Set(props.bases.map(base => base.category))
  return Array.from(categorySet)
})

// 获取每个类别的基地数量
const getCategoryCount = (category: string) => {
  return props.bases.filter(base => base.category === category).length
}

// 定义不同类别的标记图标颜色
const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    '习近平总书记足迹': '#e60000',
    '井冈山精神': '#e6a23c', 
    '红色旅游': '#AA381E',
    '革命遗址': '#f56c6c',
    '英烈纪念': '#909399'
  }
  return colors[category] || '#409eff'
}

// UTF-8 字符串转 Base64 的兼容方法
const utf8ToBase64 = (str: string) => {
  try {
    // 使用 encodeURIComponent 和 unescape 来处理 UTF-8 字符
    return btoa(unescape(encodeURIComponent(str)))
  } catch (error) {
    console.warn('Base64编码失败，使用降级方案:', error)
    // 降级方案：移除非ASCII字符
    const asciiStr = str.replace(/[^\x00-\x7F]/g, "")
    return btoa(asciiStr)
  }
}

// 图标缓存系统 - 避免重复创建相同图标
const iconCache: Record<string, any> = {}

/**
 * 获取指定类型和状态的图标，优先从缓存获取
 * @param category 基地类别
 * @param state 图标状态: 'normal', 'highlight', 'pulse1', 'pulse2'
 */
const getCachedIcon = (category: string, state: string = 'normal') => {
  // 生成缓存键
  const cacheKey = `${category}_${state}`
  
  // 如果缓存中存在，直接返回
  if (iconCache[cacheKey]) {
    return iconCache[cacheKey]
  }
  
  // 创建新图标
  let icon
  
  const baseColor = getCategoryColor(category)

  // 根据状态确定图标尺寸和样式
  const isHighlighted = state !== 'normal'
  const size = isHighlighted ? 34 : 28 // 只有普通和高亮（放大）两种尺寸
  const color = baseColor // 始终使用基础颜色，保持一致
  
  // 创建并缓存图标
  icon = createCustomIcon(category, isHighlighted, size, color)
  iconCache[cacheKey] = icon
  return icon
}

const toggleMarkerHighlight = (marker: any, shouldHighlight: boolean) => {
  try {
    const markerObj = allMarkers.find(m => m.marker === marker);
    if (!markerObj) return;

    if (marker.setZIndexOffset) {
      marker.setZIndexOffset(shouldHighlight ? 9999 : 0);
    }

    if (marker._pulseTimer) {
      clearInterval(marker._pulseTimer);
    }

    if (shouldHighlight) {
      let isEnlarged = false;
      marker._pulseTimer = setInterval(() => {
        const targetState = isEnlarged ? 'normal' : 'highlight';
        const icon = getCachedIcon(markerObj.base.category, targetState);
        marker.setIcon(icon);
        isEnlarged = !isEnlarged;
      }, 700); // Gentle breathing effect
    } else {
      const normalIcon = getCachedIcon(markerObj.base.category, 'normal');
      marker.setIcon(normalIcon);
    }
  } catch (error) {
    console.error('❌ 切换标记高亮状态失败:', error);
  }
}

/**
 * 创建自定义标记图标
 * @param category 基地类别，用于确定颜色
 * @param isHighlighted 是否处于高亮状态
 * @param customSize 可选自定义尺寸
 * @param customColor 可选自定义颜色
 * @returns 天地图Icon对象
 */
const createCustomIcon = (category: string, isHighlighted = false, customSize?: number, customColor?: string) => {
  // 使用自定义颜色或默认类别颜色
  const color = customColor || getCategoryColor(category)
  // 使用自定义尺寸或默认尺寸
  const size = customSize || (isHighlighted ? 34 : 28)
  // 锚点设在图标底部中心
  const anchor = new (window as any).T.Point(size / 2, size)
  
  // 全新、超清晰的SVG设计，旨在消除任何模糊感
  const svgContent = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 32 32">
      <!-- 外部的半透明白圈，用于和地图清晰分离，比阴影更锐利 -->
      <circle cx="16" cy="16" r="15" fill="rgba(255, 255, 255, 0.6)" />
      
      <!-- 主要的纯色圆 -->
      <circle cx="16" cy="16" r="13" fill="${color}" />
      
      <!-- 内部的白色圆，提供对比 -->
      <circle cx="16" cy="16" r="8" fill="white" />
      
      <!-- 中心纯色点 -->
      <circle cx="16" cy="16" r="5" fill="${color}" />
    </svg>
  `
  
  // 创建天地图图标对象，使用base64编码的SVG
  return new (window as any).T.Icon({
    iconUrl: `data:image/svg+xml;base64,${utf8ToBase64(svgContent)}`,
    iconSize: new (window as any).T.Point(size, size),
    iconAnchor: anchor
  })
}

// 初始化地图
const initMap = () => {
  if (!mapContainer.value || !(window as any).T) {
    isMapReady.value = false
    return
  }

  try {
    map = new (window as any).T.Map(mapContainer.value)
    const center = new (window as any).T.LngLat(props.defaultCenter.lng, props.defaultCenter.lat)
    // 严格使用传入的 props 初始化地图
    map.centerAndZoom(center, props.defaultZoom)
    
    map.enableScrollWheelZoom()
    map.addControl(new (window as any).T.Control.Zoom())
    map.addControl(new (window as any).T.Control.Scale())
    
    map.addEventListener('click', (e: any) => {
      if (!e.target || e.target === map) {
        clearHighlight()
      }
    })
    
    isMapReady.value = true
    
    // 地图准备好后，立即尝试更新一次视图
    // 这将处理数据在地图渲染前就已存在的情况
    addMarkers()
    // updateMapView()
    
  } catch (error) {
    console.error('地图初始化失败:', error)
    isMapReady.value = false
  }
}

// 创建信息窗口内容的辅助函数
const createInfoWindowContent = (base: PracticeBase) => {
  return `
    <div style="padding: 8px; max-width: 280px; background: #ffffff; border-radius: 8px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <!-- 标题区域 -->
      <div style="margin-bottom: 14px;">
        <h4 style="margin: 0 0 4px 0; color: #1f2937; font-size: 16px; font-weight: 600; line-height: 1.3;">${base.name}</h4>
        <span style="display: inline-block; background: ${getCategoryColor(base.category)}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
          ${base.category}
        </span>
      </div>
      
      <!-- 地址信息 - 点击直接导航 -->
      <a href="javascript:void(0)" 
         id="nav-link-${base.id}"
         style="display: block; 
                text-decoration: none; 
                margin-bottom: 12px; 
                padding: 12px; 
                background: #f9fafb; 
                border-radius: 6px; 
                border-left: 3px solid ${getCategoryColor(base.category)};
                transition: all 0.25s ease;
                cursor: pointer;
                position: relative;
                border: 1px solid #e5e7eb;"
         onmouseover="this.style.background='#e6f3ff'; this.style.borderColor='#409eff'; this.style.boxShadow='0 2px 8px rgba(64, 158, 255, 0.15)'; this.style.transform='translateY(-1px)';" 
         onmouseout="this.style.background='#f9fafb'; this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'; this.style.transform='translateY(0)';">
        
        <div style="display: flex; align-items: flex-start; gap: 6px;">
          <span style="color: ${getCategoryColor(base.category)}; font-size: 14px; margin-top: 1px;">📍</span>
          <div style="flex: 1;">
            <p style="margin: 0 0 4px 0; color: #374151; font-size: 13px; font-weight: 500; line-height: 1.4; text-decoration: underline; text-decoration-color: ${getCategoryColor(base.category)}; text-underline-offset: 2px;">${base.address}</p>
            ${base.phone ? `<p style="margin: 0; color: #6b7280; font-size: 12px;">📞 ${base.phone}</p>` : ''}
          </div>
          <!-- 导航图标提示 -->
          <span style="color: #409eff; font-size: 16px; opacity: 0.7; transition: all 0.2s ease;">🧭</span>
        </div>
      </a>
      
      <!-- 操作按钮 -->
      <div style="display: flex; gap: 8px; justify-content: flex-end; margin-top: 8px; align-items: center;">
        <a href="javascript:void(0)" id="view-details-${base.id}"
           onmouseover="this.style.color='#1e40af'; this.style.textDecoration='underline';" 
           onmouseout="this.style.color='#2563eb'; this.style.textDecoration='none';"
           style="color: #2563eb; text-decoration: none; font-size: 13px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 4px; transition: all 0.2s ease;">
          <span>查看基地详情页</span>
          <span style="font-size: 12px;">→</span>
        </a>
      </div>
    </div>
  `
}

// 绑定信息窗口内的事件
const bindInfoWindowEvents = (base: PracticeBase) => {
  // 绑定导航事件
  const navButton = document.getElementById(`nav-link-${base.id}`)
  if (navButton) {
    navButton.onclick = (e) => {
      e.preventDefault()
      e.stopPropagation()
      handleNavigation(base)
    }
  }

  // 绑定查看详情事件
  const detailsButton = document.getElementById(`view-details-${base.id}`)
  if (detailsButton) {
    detailsButton.onclick = (e) => {
      e.preventDefault()
      e.stopPropagation()
      emit('viewDetails', base)
    }
  }
}

// 获取信息窗口偏移量
const getInfoWindowOffset = () => {
  if (isMobile.value) {
    return highlightedMarkerId.value ? 
      new (window as any).T.Point(0, -120) : // 移动端高亮状态偏移
      new (window as any).T.Point(0, -100)   // 移动端普通状态偏移
  } else {
    return highlightedMarkerId.value ? 
      new (window as any).T.Point(0, -60) :  // 桌面端高亮状态偏移
      new (window as any).T.Point(0, -40)    // 桌面端普通状态偏移
  }
}

// 添加标记点
const addMarkers = () => {
  if (!map) return
  
  // 清除现有标记
  clearMarkers()
  
  props.bases.forEach(base => {
    try {
      // 创建天地图坐标点
      const point = new (window as any).T.LngLat(base.coordinates.lng, base.coordinates.lat)
      
      // 检查是否应该高亮（与当前高亮ID匹配）
      const isHighlighted = highlightedMarkerId.value === base.id
      
      // 创建标记，高亮状态使用特殊图标
      const marker = new (window as any).T.Marker(point, {
        icon: createCustomIcon(base.category, isHighlighted)
      })
      
      // 创建标记对象，包含基地信息
      const markerObj = {
        marker,
        base,
        category: base.category,
        point
      }
      
      // 添加到所有标记数组
      allMarkers.push(markerObj)
      
      // 检查是否应该显示（根据筛选条件）
      const shouldShow = activeLegendItems.value.size === 0 || activeLegendItems.value.has(base.category)
      
      if (shouldShow) {
        // 添加标记到地图
        map.addOverLay(marker)
        markers.push(markerObj)
      }
      
      // 创建信息窗口
      const infoWindowContent = createInfoWindowContent(base)
      const infoWindow = new (window as any).T.InfoWindow(infoWindowContent, {
        offset: getInfoWindowOffset()
      })
      
      // 标记点击事件
      marker.addEventListener('click', () => {
        map.openInfoWindow(infoWindow, point)
        // 只触发列表定位事件，不触发页面跳转
        emit('baseLocate', base)
        
        // 使用nextTick确保DOM已经渲染，然后绑定所有内部事件
        nextTick(() => {
          bindInfoWindowEvents(base)
        })
      })
      
    } catch (error) {
      console.error(`❌ 添加标记失败 (${base.name}):`, error)
    }
  })
}

// 清除标记
const clearMarkers = () => {
  markers.forEach(markerObj => {
    map.removeOverLay(markerObj.marker)
  })
  markers = []
  allMarkers = []
}

// 图例点击处理函数
const handleLegendClick = (category: string) => {
  console.log('图例点击:', category)
  if (activeLegendItems.value.has(category)) {
    // 如果已选中，则取消选中
    activeLegendItems.value.delete(category)
    console.log('取消选中:', category)
  } else {
    // 如果未选中，则选中
    activeLegendItems.value.add(category)
    console.log('选中:', category)
  }
  
  // 触发筛选
  filterMarkers()
  
  // 自动调整视图以适应筛选后的基地
  setTimeout(() => {
    updateMapView()
  }, 300)
}

// 筛选标记点
const filterMarkers = () => {
  if (!map) return
  
  allMarkers.forEach(markerObj => {
    const shouldShow = activeLegendItems.value.size === 0 || activeLegendItems.value.has(markerObj.category)
    
    if (shouldShow) {
      // 显示标记
      map.addOverLay(markerObj.marker)
    } else {
      // 隐藏标记
      map.removeOverLay(markerObj.marker)
      
      // 如果隐藏的标记是当前高亮的，清除高亮状态
      if (highlightedMarkerId.value === markerObj.base.id) {
        clearHighlight()
      }
    }
  })
  
  // 更新显示的标记数组
  markers = allMarkers.filter(markerObj => 
    activeLegendItems.value.size === 0 || activeLegendItems.value.has(markerObj.category)
  )
}

// 获取当前显示的基地列表
const getVisibleBases = () => {
  if (activeLegendItems.value.size === 0) {
    return props.bases
  }
  return props.bases.filter(base => activeLegendItems.value.has(base.category))
}

// 重置筛选
const resetFilter = () => {
  console.log('重置图例筛选')
  activeLegendItems.value.clear()
  filterMarkers()
  
  // 自动调整视图以适应所有基地
  setTimeout(() => {
    updateMapView()
  }, 300)
}

// 更新地图视图 - 使用天地图兼容的方法
const updateMapView = () => {
  if (!map || !isMapReady.value) {
    console.log('地图未准备好，跳过视图更新')
    return
  }

  // 1. 解决布局问题：确保地图铺满容器
  map.checkResize()
}

// 清除高亮状态
const clearHighlight = () => {
  if (highlightedMarkerId.value) {
    // 找到高亮的标记并移除CSS高亮效果
    const highlightedMarker = allMarkers.find(m => m.base.id === highlightedMarkerId.value)
    if (highlightedMarker) {
      toggleMarkerHighlight(highlightedMarker.marker, false)
    }
    highlightedMarkerId.value = null
  }
}

/**
 * 高亮指定基地
 * @param baseId 要高亮的基地ID
 */
const highlightBase = (baseId: string) => {
  // 先清除之前的高亮
  clearHighlight()
  
  // 设置新的高亮
  const targetMarker = allMarkers.find(m => m.base.id === baseId)
  if (targetMarker) {
    // 应用高亮效果
    toggleMarkerHighlight(targetMarker.marker, true)
    highlightedMarkerId.value = baseId
    
    // 将地图中心移动到该基地
    map.panTo(targetMarker.point)
    
    console.log('✨ 高亮显示基地:', targetMarker.base.name)
  } else {
    console.warn('⚠️ 未找到目标基地:', baseId)
  }
}

// 定位到基地并打开信息窗口
const locateAndOpenInfoWindow = (base: PracticeBase) => {
  console.log('🎯 MapView - 定位并打开信息窗口:', base.name)
  
  if (!map || !isMapReady.value) {
    console.warn('⚠️ 地图未准备好')
    return
  }
  
  // 找到对应的标记
  const targetMarker = allMarkers.find(m => m.base.id === base.id)
  if (!targetMarker) {
    console.warn('⚠️ 未找到对应的标记:', base.name)
    return
  }
  
  // 移动地图中心到该基地
  const point = new (window as any).T.LngLat(base.coordinates.lng, base.coordinates.lat)
  
  // 创建一个动画效果，先缩小再放大
  const currentZoom = map.getZoom()
  const intermediateZoom = Math.max(currentZoom - 2, props.defaultZoom - 1)
  // 计算目标缩放级别，但确保不超过defaultZoom
  const baseTargetZoom = isMobile.value ? 16 : 17 // 移动端和桌面端的基础缩放级别
  const targetZoom = Math.min(baseTargetZoom, props.defaultZoom) // 确保不超过defaultZoom
  
  // 步骤1: 平滑移动到目标点，同时缩小视图
  map.panTo(point)
  
  // 延迟执行后续动作，确保平移已经开始
  setTimeout(() => {
    // 步骤2: 缩小地图创建视觉效果
    if (currentZoom > intermediateZoom) {
      map.setZoom(intermediateZoom)
    }
    
    // 步骤3: 放大并高亮显示目标点
    setTimeout(() => {
      // 应用高亮效果
      highlightBase(base.id)
      
      // 平滑缩放到目标级别
      map.setZoom(targetZoom)
      console.log('📌 定位到基地，缩放级别:', targetZoom, '(defaultZoom:', props.defaultZoom, ')')
      
      // 确保目标在中心
      map.panTo(point)
      
      // 步骤4: 打开信息窗口
      setTimeout(() => {
        const infoWindowContent = createInfoWindowContent(base)
        const infoWindow = new (window as any).T.InfoWindow(infoWindowContent, {
          offset: getInfoWindowOffset()
        })
        
        map.openInfoWindow(infoWindow, point)
        
        // 绑定导航事件和详情事件
        nextTick(() => {
          bindInfoWindowEvents(base)
        })
      }, 500) // 信息窗口延迟
    }, 300) // 缩放延迟
  }, 200) // 平移延迟
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
  if (map) {
    map.checkResize()
  }
}

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
  
  // 等待天地图API加载完成
  const checkTiandituApi = () => {
    if ((window as any).T) {
      initMap()
    } else {
      console.log('等待天地图API加载...')
      setTimeout(checkTiandituApi, 100)
    }
  }
  
  checkTiandituApi()
})

// 在组件卸载时清理资源
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  
  // 清除所有标记的脉冲定时器
  allMarkers.forEach(markerObj => {
    if (markerObj.marker && markerObj.marker._pulseTimer) {
      clearInterval(markerObj.marker._pulseTimer)
      markerObj.marker._pulseTimer = null
    }
  })
  
  // 清除地图实例
  if (map) {
    // 移除所有覆盖物
    map.clearOverLays && map.clearOverLays()
    
    // 移除所有事件监听
    map.removeEventListener && map.removeEventListener('click')
    map.removeEventListener && map.removeEventListener('zoomend')
    map.removeEventListener && map.removeEventListener('moveend')
    
    // 释放地图DOM引用
    if (mapContainer.value) {
      mapContainer.value.innerHTML = ''
    }
    
    // 清空图标缓存
    Object.keys(iconCache).forEach(key => {
      delete iconCache[key]
    })
    
    console.log('🧹 清理地图组件资源')
  }
})

// 监听props变化 - 这是解决问题的核心
watch(() => props.bases, (newBases) => {
  if (!isMapReady.value) {
    return;
  }

  // 1. 无论何时，数据变化都重新渲染所有标记点
  addMarkers();

  // 2. 动效的关键：总是调用 updateMapView
  //    - 对于首次加载，这会创建用户期望的“初始动画”
  //    - 对于后续筛选，这会创建平滑的“自适应视图”
  //    - 这意味着地图将优先展示所有点位，而不是固守 `defaultZoom`
  console.log(`🗺️ MapView: 数据变化，即将通过动效更新视图以显示 ${newBases.length} 个标记。`);
  updateMapView();
  
}, { deep: true });

// 监听高亮ID变化
watch(() => highlightedMarkerId.value, (newId, oldId) => {
  if (!isMapReady.value || !map) return
  
  console.log('高亮ID变化:', { oldId, newId })
  
  // 处理旧高亮标记
  if (oldId) {
    const oldMarker = allMarkers.find(m => m.base.id === oldId)
    if (oldMarker) {
      toggleMarkerHighlight(oldMarker.marker, false)
    }
  }
  
  // 处理新高亮标记
  if (newId) {
    const newMarker = allMarkers.find(m => m.base.id === newId)
    if (newMarker) {
      toggleMarkerHighlight(newMarker.marker, true)
      
      // 如果是通过外部属性变化触发的高亮，可能需要平移地图
      map.panTo(newMarker.point)
    }
  }
})

// 暴露方法供父组件调用
defineExpose({
  highlightBase,
  clearHighlight,
  updateMapView,
  locateAndOpenInfoWindow,
  autoFitView: updateMapView // 为了兼容 Home.vue 中的调用
})
</script>

<template>
  <div class="map-container">
    <!-- 地图容器 -->
    <div ref="mapContainer" class="map-view"></div>
    
    <!-- 地图图例 -->
    <div v-if="!hideMapLegend" class="map-legend" :class="{ 'mobile': isMobile }">
      <div class="legend-header">
        <span class="legend-title">基地类型</span>
        <button 
          class="legend-reset" 
          @click="resetFilter"
          :disabled="activeLegendItems.size === 0"
        >
          重置
        </button>
      </div>
      
      <div class="legend-items">
        <div 
          v-for="category in categories" 
          :key="category"
          class="legend-item"
          :class="{ 
            'active': activeLegendItems.has(category),
            'inactive': activeLegendItems.size > 0 && !activeLegendItems.has(category)
          }"
          @click="handleLegendClick(category)"
        >
          <span class="legend-color" :style="{ backgroundColor: getCategoryColor(category) }"></span>
          <span class="legend-text">{{ category }}</span>
          <span class="legend-count">({{ getCategoryCount(category) }})</span>
        </div>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div v-if="!props.hideMapStats" class="map-stats" :class="{ 'mobile': isMobile }">
      <div class="stats-item">
        <span class="stats-label">总基地数</span>
        <span class="stats-value">{{ props.bases.length }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">显示中</span>
        <span class="stats-value">{{ getVisibleBases().length }}</span>
      </div>
    </div>
    
    <!-- 地图加载提示 -->
    <div v-if="!isMapReady" class="map-loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在加载地图...</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.map-view {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  /* 通过CSS滤镜提升地图底色的亮度和饱和度，营造更鲜明的视觉效果 */
  filter: brightness(105%) saturate(110%) contrast(95%);
  transition: filter 0.3s ease;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.loading-content {
  text-align: center;
  padding: 24px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-content p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.map-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 9000;
  max-width: 280px;
  font-size: 13px;
}

.map-legend.mobile {
  z-index: 9500;
  max-width: 240px;
  padding: 12px;
  backdrop-filter: blur(8px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.legend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.legend-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.legend-reset {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.legend-reset:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

.legend-reset:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.legend-item:hover {
  background: #f3f4f6;
}

.legend-item.active {
  background: #dbeafe;
  border: 1px solid #3b82f6;
}

.legend-item.inactive {
  opacity: 0.5;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.legend-text {
  flex: 1;
  color: #374151;
  font-weight: 500;
  line-height: 1.4;
}

.legend-count {
  color: #6b7280;
  font-size: 12px;
}

.map-stats {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 9000;
  display: flex;
  gap: 16px;
  font-size: 13px;
}

.map-stats.mobile {
  z-index: 9500;
  padding: 12px;
  gap: 12px;
  backdrop-filter: blur(8px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stats-label {
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
}

.stats-value {
  color: #1f2937;
  font-size: 18px;
  font-weight: 700;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .map-legend {
    left: 10px;
    top: 10px;
    max-width: 200px;
    padding: 12px;
    font-size: 12px;
  }
  
  .map-stats {
    right: 10px;
    top: 10px;
    padding: 12px;
    gap: 12px;
  }
  
  .stats-value {
    font-size: 16px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .legend-item {
    min-height: 44px;
  }
  
  .legend-item:active {
    background: #e5e7eb;
    transform: scale(0.98);
  }
}
</style>

<!-- 全局CSS动画样式 - 不能使用scoped -->
<style>
/**
 * 天地图标记点高亮动画
 * 这些CSS选择器针对天地图API的DOM结构优化
 */

/* 标记点脉冲动画关键帧 */
@keyframes marker-pulse-animation {
  0% {
    transform: scale(1.0);
    opacity: 1.0;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.7));
  }
  50% {
    transform: scale(1.25); 
    opacity: 0.85;
    filter: drop-shadow(0 0 10px rgba(212, 166, 106, 0.8)) brightness(1.15); /* 改为琉璃金 */
  }
  100% {
    transform: scale(1.0);
    opacity: 1.0;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.7));
  }
}

/* 光晕外环动画关键帧 */
@keyframes marker-glow-animation {
  0% {
    box-shadow: 0 0 0 0 rgba(212, 166, 106, 0.4), 0 0 8px 2px rgba(212, 166, 106, 0.3); /* 改为琉璃金 */
    opacity: 0.6;
  }
  50% {
    box-shadow: 0 0 0 6px rgba(212, 166, 106, 0.2), 0 0 12px 8px rgba(212, 166, 106, 0.1); /* 改为琉璃金 */
    opacity: 0.9;
  }
  100% {
    box-shadow: 0 0 0 0 rgba(212, 166, 106, 0.4), 0 0 8px 2px rgba(212, 166, 106, 0.3); /* 改为琉璃金 */
    opacity: 0.6;
  }
}

/**
 * 天地图标记高亮样式
 * 完整兼容所有天地图API版本的DOM结构
 */

/* 天地图v4.0 API标准标记图标 */
.marker-highlighted .tdt-marker-icon {
  animation: marker-pulse-animation 1.5s ease-in-out infinite;
  transform-origin: center bottom;
  z-index: 9999 !important;
}

/* 确保兼容其他可能的DOM结构 */
.marker-highlighted img,
.marker-highlighted svg,
.marker-highlighted canvas,
.marker-highlighted > div {
  animation: marker-pulse-animation 1.5s ease-in-out infinite;
  transform-origin: center bottom;
  z-index: 9999 !important;
}

/* 标记容器元素 */
.marker-highlighted {
  position: relative !important;
  z-index: 9999 !important;
  opacity: 1 !important;
}

/* 光晕外环效果 */
.marker-highlighted:before {
  content: '';
  position: absolute;
  width: 36px;
  height: 36px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: -1;
  animation: marker-glow-animation 2s ease-in-out infinite;
  pointer-events: none;
}

/* 强调高亮效果 - 用于临时强调 */
.super-highlight .tdt-marker-icon,
.super-highlight img,
.super-highlight svg,
.super-highlight > div {
  animation: marker-pulse-animation 0.8s ease-in-out infinite !important;
  animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1) !important;
  filter: drop-shadow(0 0 12px rgba(212, 166, 106, 0.9)) brightness(1.2) !important; /* 改为琉璃金 */
  z-index: 10000 !important;
  transform-origin: center bottom !important;
}

/* 强调高亮的光环效果 */
.super-highlight:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background: radial-gradient(circle, rgba(212, 166, 106, 0.4) 0%, rgba(212, 166, 106, 0) 70%); /* 改为琉璃金 */
  animation: marker-glow-animation 0.8s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}
</style>