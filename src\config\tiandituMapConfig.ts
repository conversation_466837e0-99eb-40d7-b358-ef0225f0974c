// 天地图API配置
export interface TiandituMapApiConfig {
  tk: string // 天地图API密钥
  width?: number // 图片宽度，默认400
  height?: number // 图片高度，默认300
  zoom?: number // 缩放级别，默认15
}

import { env } from './env'

// 天地图配置
export const tiandituMapConfig: TiandituMapApiConfig = {
  // 从环境变量获取天地图API密钥
  tk: env.TIANDITU_MAP_TK,
  
  // 静态地图图片配置
  width: 400,  // 图片宽度
  height: 300, // 图片高度
  zoom: 16     // 缩放级别，16级可以显示较详细的地理信息
}

// 验证配置是否完整
export function validateTiandituMapConfig(): boolean {
  if (!tiandituMapConfig.tk) {
    console.warn('⚠️  天地图API密钥未配置，将使用默认图片')
    console.warn('请设置环境变量 VITE_TIANDITU_MAP_TK')
    return false
  }
  
  console.log('✅ 天地图API配置已加载')
  return true
}

// 天地图API服务地址
export const TIANDITU_API_URLS = {
  // 影像底图
  img: 'https://t{s}.tianditu.gov.cn/img_w/wmts',
  // 影像注记
  cia: 'https://t{s}.tianditu.gov.cn/cia_w/wmts',
  // 矢量底图
  vec: 'https://t{s}.tianditu.gov.cn/vec_w/wmts',
  // 矢量注记
  cva: 'https://t{s}.tianditu.gov.cn/cva_w/wmts',
  // 地形底图
  ter: 'https://t{s}.tianditu.gov.cn/ter_w/wmts',
  // 地形注记
  cta: 'https://t{s}.tianditu.gov.cn/cta_w/wmts'
}

// 环境变量配置说明
export const ENV_SETUP_GUIDE = `
天地图配置步骤：
1. 在项目根目录创建 .env 文件
2. 添加以下内容：
   VITE_BAIDU_MAP_AK=your_baidu_map_api_key_here
   VITE_TIANDITU_MAP_TK=your_tianditu_map_api_key_here
3. 重启开发服务器

获取API密钥：
- 百度地图: https://lbsyun.baidu.com/apiconsole/key
- 天地图: https://console.tianditu.gov.cn/api/key

天地图官方文档：http://lbs.tianditu.gov.cn/api/js4.0/guide.html
` 