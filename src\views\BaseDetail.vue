<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft, Location, Phone, Clock, Star, Calendar, Link } from '@element-plus/icons-vue'
import { mockBases } from '../data/mockData'
import type { PracticeBase } from '../types'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const currentBase = ref<PracticeBase | null>(null)
const mapContainer = ref<HTMLElement>()
const iframeLoading = ref(true)
const iframeError = ref(false)
let detailMap: any = null

const baseId = computed(() => route.params.id as string)

const goBack = () => {
  router.go(-1)
}

const getDirections = () => {
  if (currentBase.value) {
    // 使用百度地图导航
    const url = `https://api.map.baidu.com/direction?origin=我的位置&destination=${currentBase.value.address}&mode=driving&region=江西省&output=html`
    window.open(url, '_blank')
  }
}

const callPhone = () => {
  if (currentBase.value?.phone) {
    window.location.href = `tel:${currentBase.value.phone}`
  }
}

const openExternalWebsite = () => {
  if (currentBase.value?.websiteUrl) {
    window.open(currentBase.value.websiteUrl, '_blank')
  }
}



// 获取历史背景内容
const getHistoryContent = (base: PracticeBase | null) => {
  if (!base) return ''
  
  const historyMap: Record<string, string> = {
    '井冈山革命博物馆': '井冈山革命博物馆始建于1958年，是中国第一个地方性革命史类博物馆。2016年，习近平总书记在井冈山调研时专程前往参观，强调要结合新的时代条件，坚持坚定执着追理想、实事求是闯新路、艰苦奋斗攻难关、依靠群众求胜利，让井冈山精神放射出新的时代光芒。',
    '西柏坡纪念馆': '西柏坡纪念馆建于1987年，是为缅怀井冈山革命斗争时期壮烈牺牲的革命先烈而兴建的。纪念馆内安葬着在井冈山斗争中牺牲的革命烈士，是进行革命传统教育和爱国主义教育的重要场所。',
    '黄洋界哨口': '黄洋界保卫战发生在1928年8月30日，是井冈山根据地军民在毛泽东、朱德领导下，打破国民党军队对井冈山革命根据地第二次"会剿"的一次著名战斗。这里是井冈山精神的重要发源地之一。',
    '茨坪毛泽东旧居': '1927年10月，毛泽东率领秋收起义部队上井冈山，在茨坪建立了革命根据地。这里是毛泽东同志在井冈山期间经常居住和办公的地方，见证了井冈山革命根据地的建立和发展。'
  }
  
  return historyMap[base.name] || '这里承载着深厚的革命历史文化底蕴，是开展红色教育、传承革命精神的重要场所，见证了中国共产党领导人民进行革命斗争的光辉历程。'
}

// 获取参观亮点
const getHighlights = (base: PracticeBase | null) => {
  if (!base) return []
  
  const highlightsMap: Record<string, Array<{icon: string, title: string, desc: string}>> = {
    '井冈山革命博物馆': [
      { icon: '🏛️', title: '珍贵文物', desc: '馆藏文物3万余件，包括毛泽东用过的砚台、朱德的扁担等珍贵革命文物' },
      { icon: '📽️', title: '多媒体展示', desc: '运用现代科技手段，生动再现井冈山斗争的历史场景' },
      { icon: '🎯', title: '专题展览', desc: '定期举办专题展览，深入解读井冈山精神的时代价值' }
    ],
    '西柏坡纪念馆': [
      { icon: '🕊️', title: '庄严纪念', desc: '纪念堂内安放着部分老红军和革命先烈的骨灰' },
      { icon: '🌿', title: '碑林景观', desc: '碑林镌刻着毛泽东等老一辈革命家的亲笔题词' },
      { icon: '⭐', title: '雕像园', desc: '矗立着毛泽东、朱德、陈毅、彭德怀等革命家的雕像' }
    ],
    '黄洋界哨口': [
      { icon: '🏔️', title: '险峻地势', desc: '海拔1343米，地势险要，是井冈山的天然屏障' },
      { icon: '💣', title: '保卫战遗址', desc: '1928年黄洋界保卫战的历史现场，见证了红军的英勇' },
      { icon: '🌅', title: '壮美景色', desc: '可观云海日出，体验"黄洋界上炮声隆"的壮阔意境' }
    ]
  }
  
  return highlightsMap[base.name] || [
    { icon: '🏛️', title: '历史文化', desc: '深厚的红色文化底蕴和革命历史传承' },
    { icon: '🌟', title: '教育意义', desc: '开展爱国主义教育和革命传统教育的重要基地' },
    { icon: '📚', title: '学习体验', desc: '通过实地参观学习，感受革命精神的力量' }
  ]
}

// 获取最佳参观时间
const getBestVisitTime = (base: PracticeBase | null) => {
  if (!base) return ''
  return '春秋两季（3-5月、9-11月），气候宜人，适合参观游览'
}

// 获取建议游览时长
const getVisitDuration = (base: PracticeBase | null) => {
  if (!base) return ''
  
  const durationMap: Record<string, string> = {
    '井冈山革命博物馆': '2-3小时',
    '西柏坡纪念馆': '1-2小时',
    '黄洋界哨口': '1.5-2小时',
    '茨坪毛泽东旧居': '1-1.5小时'
  }
  
  return durationMap[base.name] || '1-2小时'
}

// 获取交通信息
const getTransportInfo = (base: PracticeBase | null) => {
  if (!base) return ''
  
  const transportMap: Record<string, string> = {
    '井冈山革命博物馆': '井冈山机场出发约30分钟车程，市内可乘坐公交或出租车到达',
    '西柏坡纪念馆': '位于茨坪镇内，步行或乘坐公交车均可到达',
    '黄洋界哨口': '从茨坪出发约17公里，建议自驾或乘坐旅游专线',
    '茨坪毛泽东旧居': '位于茨坪镇中心，步行即可到达'
  }
  
  return transportMap[base.name] || '可通过公共交通或自驾方式到达，建议提前查询最新交通信息'
}

// 获取周边推荐
const getNearbyRecommendations = (base: PracticeBase | null) => {
  if (!base) return []
  
  const nearbyMap: Record<string, Array<{name: string, distance: string, desc: string}>> = {
    '井冈山革命博物馆': [
      { name: '西柏坡纪念馆', distance: '500米', desc: '缅怀革命先烈的庄严场所' },
      { name: '茨坪毛泽东旧居', distance: '800米', desc: '毛泽东同志在井冈山的居住地' },
      { name: '挹翠湖公园', distance: '1公里', desc: '风景优美的休闲公园' }
    ],
    '西柏坡纪念馆': [
      { name: '井冈山革命博物馆', distance: '500米', desc: '了解井冈山革命历史的重要场馆' },
      { name: '茨坪革命旧址群', distance: '1公里', desc: '多处革命历史遗迹' },
      { name: '龙潭瀑布', distance: '3公里', desc: '自然风光与红色文化的完美结合' }
    ],
    '黄洋界哨口': [
      { name: '大井毛泽东旧居', distance: '8公里', desc: '毛泽东读书石和感情的地方' },
      { name: '小井红军医院', distance: '6公里', desc: '中国红军第一所正规医院' },
      { name: '八角楼', distance: '12公里', desc: '毛泽东写作《中国的红色政权为什么能够存在？》的地方' }
    ]
  }
  
  return nearbyMap[base.name] || [
    { name: '井冈山革命博物馆', distance: '3公里', desc: '了解井冈山革命历史' },
    { name: '龙潭风景区', distance: '5公里', desc: '欣赏自然风光' },
    { name: '茨坪镇中心', distance: '2公里', desc: '餐饮住宿集中区域' }
  ]
}

const initDetailMap = () => {
  if (!mapContainer.value || !currentBase.value || !(window as any).BMap) {
    return
  }

  try {
    // 创建地图实例
    detailMap = new (window as any).BMap.Map(mapContainer.value)
    
    // 设置基地位置
    const point = new (window as any).BMap.Point(
      currentBase.value.coordinates.lng, 
      currentBase.value.coordinates.lat
    )
    
    // 初始化地图
    detailMap.centerAndZoom(point, 15)
    detailMap.enableScrollWheelZoom(true)
    
    // 添加控件
    detailMap.addControl(new (window as any).BMap.NavigationControl())
    detailMap.addControl(new (window as any).BMap.ScaleControl())
    
    // 创建自定义标记
    const createDetailIcon = () => {
      return new (window as any).BMap.Icon(
        `data:image/svg+xml;base64,${btoa(`
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="50" viewBox="0 0 40 50">
            <path d="M20 0C9 0 0 9 0 20c0 20 20 30 20 30s20-10 20-30C40 9 31 0 20 0z" fill="#409eff"/>
            <circle cx="20" cy="20" r="10" fill="white"/>
            <circle cx="20" cy="20" r="6" fill="#409eff"/>
          </svg>
        `)}`,
        new (window as any).BMap.Size(40, 50),
        {
          anchor: new (window as any).BMap.Size(20, 50)
        }
      )
    }
    
    // 添加标记
    const marker = new (window as any).BMap.Marker(point, {
      icon: createDetailIcon()
    })
    detailMap.addOverlay(marker)
    
    // 添加信息窗口
    const infoWindow = new (window as any).BMap.InfoWindow(`
      <div style="padding: 10px;">
        <h4 style="margin: 0 0 8px 0; color: #303133;">${currentBase.value.name}</h4>
        <p style="margin: 4px 0; color: #606266; font-size: 14px;">${currentBase.value.address}</p>
      </div>
    `)
    
    marker.addEventListener('click', () => {
      detailMap.openInfoWindow(infoWindow, point)
    })
    
    // 添加动画效果
    marker.setAnimation(window.BMAP_ANIMATION_BOUNCE)
    setTimeout(() => {
      marker.setAnimation(null)
    }, 2000)
    
  } catch (error) {
    console.error('详情地图初始化失败:', error)
  }
}

onMounted(() => {
  loading.value = true
  // 重置iframe状态
  iframeLoading.value = true
  iframeError.value = false
  
  // Simulate API call
  setTimeout(() => {
    currentBase.value = mockBases.find(base => base.id === baseId.value) || null
    loading.value = false
    
    // 初始化地图
    if (currentBase.value) {
      setTimeout(() => {
        initDetailMap()
      }, 100)
    }
  }, 500)
})
</script>

<template>
  <div class="base-detail-container">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="currentBase" class="detail-content">
      <!-- Header -->
      <div class="detail-header">
        <div class="header-actions">
          <el-button @click="goBack" circle size="large">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
        </div>
        <div class="header-image">
          <img :src="currentBase.imageUrl" :alt="currentBase.name" />
          <div class="overlay">
            <h1 class="base-name">{{ currentBase.name }}</h1>
            <div class="base-meta">
              <el-tag type="primary" size="large">{{ currentBase.category }}</el-tag>
              <el-tag size="large">{{ currentBase.city }}</el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- Content -->
      <div class="detail-body">
        <div class="container">
          <el-row :gutter="32">
            <el-col :xs="24" :lg="16">
              <!-- Basic Info -->
              <el-card class="info-card" shadow="never">
                <template #header>
                  <h3>基本信息</h3>
                </template>
                <div class="info-grid">
                  <div class="info-item">
                    <el-icon class="info-icon"><Location /></el-icon>
                    <div>
                      <p class="info-label">地址</p>
                      <p class="info-value">{{ currentBase.address }}</p>
                    </div>
                  </div>
                  <div class="info-item" v-if="currentBase.phone">
                    <el-icon class="info-icon"><Phone /></el-icon>
                    <div>
                      <p class="info-label">联系电话</p>
                      <p class="info-value">{{ currentBase.phone }}</p>
                    </div>
                  </div>
                  <div class="info-item" v-if="currentBase.openHours">
                    <el-icon class="info-icon"><Clock /></el-icon>
                    <div>
                      <p class="info-label">开放时间</p>
                      <p class="info-value">{{ currentBase.openHours }}</p>
                    </div>
                  </div>
                  <div class="info-item">
                    <el-icon class="info-icon"><Star /></el-icon>
                    <div>
                      <p class="info-label">评分</p>
                      <p class="info-value">{{ currentBase.rating }}/5.0</p>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- Description -->
              <el-card class="description-card" shadow="never">
                <template #header>
                  <h3>详细介绍</h3>
                </template>
                <div class="description-content">
                  <p>{{ currentBase.description }}</p>
                </div>
              </el-card>

              <!-- Features -->
              <el-card v-if="currentBase.features && currentBase.features.length > 0" class="features-card" shadow="never">
                <template #header>
                  <h3>设施特色</h3>
                </template>
                <div class="features-grid">
                  <el-tag 
                    v-for="feature in currentBase.features" 
                    :key="feature"
                    type="info"
                    size="large"
                    class="feature-tag"
                  >
                    {{ feature }}
                  </el-tag>
                </div>
              </el-card>

              <!-- Rich Content Section -->
              <el-card class="rich-content-card" shadow="never">
                <template #header>
                  <div class="content-header">
                    <h3>详细展示</h3>
                    <el-button v-if="currentBase.websiteUrl" type="primary" size="small" @click="openExternalWebsite">
                      <el-icon><Link /></el-icon>
                      访问官方网站
                    </el-button>
                  </div>
                </template>
                <div class="rich-content">
                  <!-- 历史背景 -->
                  <div class="content-section">
                    <h4 class="section-title">
                      <el-icon><Calendar /></el-icon>
                      历史背景
                    </h4>
                    <div class="section-content">
                      <p>{{ getHistoryContent(currentBase) }}</p>
                    </div>
                  </div>

                  <!-- 参观亮点 -->
                  <div class="content-section">
                    <h4 class="section-title">
                      <el-icon><Star /></el-icon>
                      参观亮点
                    </h4>
                    <div class="highlights-grid">
                      <div v-for="highlight in getHighlights(currentBase)" :key="highlight.title" class="highlight-item">
                        <div class="highlight-icon">{{ highlight.icon }}</div>
                        <div class="highlight-content">
                          <h5>{{ highlight.title }}</h5>
                          <p>{{ highlight.desc }}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 实用信息 -->
                  <div class="content-section">
                    <h4 class="section-title">
                      <el-icon><Location /></el-icon>
                      实用信息
                    </h4>
                    <div class="practical-info">
                      <div class="info-row">
                        <span class="info-label">最佳参观时间：</span>
                        <span class="info-value">{{ getBestVisitTime(currentBase) }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">建议游览时长：</span>
                        <span class="info-value">{{ getVisitDuration(currentBase) }}</span>
                      </div>
                      <div class="info-row">
                        <span class="info-label">交通方式：</span>
                        <span class="info-value">{{ getTransportInfo(currentBase) }}</span>
                      </div>
                      <div v-if="currentBase.websiteUrl" class="info-row">
                        <span class="info-label">官方网站：</span>
                        <a :href="currentBase.websiteUrl" target="_blank" class="website-link">
                          {{ currentBase.websiteUrl }}
                        </a>
                      </div>
                    </div>
                  </div>

                  <!-- 周边推荐 -->
                  <div class="content-section">
                    <h4 class="section-title">
                      <el-icon><Location /></el-icon>
                      周边推荐
                    </h4>
                    <div class="nearby-recommendations">
                      <div v-for="nearby in getNearbyRecommendations(currentBase)" :key="nearby.name" class="nearby-item">
                        <div class="nearby-distance">{{ nearby.distance }}</div>
                        <div class="nearby-info">
                          <h5>{{ nearby.name }}</h5>
                          <p>{{ nearby.desc }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>

            <el-col :xs="24" :lg="8">
              <!-- Actions -->
              <el-card class="actions-card" shadow="never">
                <template #header>
                  <h3>快速操作</h3>
                </template>
                <div class="actions-grid">
                  <el-button type="primary" size="large" @click="getDirections" block>
                    <el-icon><Location /></el-icon>
                    导航前往
                  </el-button>
                  <el-button 
                    v-if="currentBase.phone" 
                    type="success" 
                    size="large" 
                    @click="callPhone"
                    block
                  >
                    <el-icon><Phone /></el-icon>
                    拨打电话
                  </el-button>
                </div>
              </el-card>

              <!-- Map -->
              <el-card class="map-card" shadow="never">
                <template #header>
                  <h3>位置地图</h3>
                </template>
                <div class="mini-map">
                  <div ref="mapContainer" class="detail-map"></div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    
    <div v-else class="error-state">
      <el-empty description="找不到该实践基地">
        <el-button type="primary" @click="goBack">返回首页</el-button>
      </el-empty>
    </div>
  </div>
</template>

<style scoped>
.base-detail-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.loading-container {
  padding: 40px;
}

.detail-header {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.header-actions {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}

.header-image {
  position: relative;
  height: 100%;
  width: 100%;
}

.header-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 40px;
}

.base-name {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.base-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.detail-body {
  padding: 40px 0;
}

.info-card,
.description-card,
.features-card,
.rich-content-card,
.actions-card,
.map-card {
  margin-bottom: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-icon {
  color: #409eff;
  font-size: 20px;
  margin-top: 2px;
}

.info-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.description-content {
  line-height: 1.8;
  color: #606266;
}

.features-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.feature-tag {
  font-size: 14px;
  padding: 8px 16px;
}

.actions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mini-map {
  height: 250px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.detail-map {
  width: 100%;
  height: 100%;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.content-header h3 {
  margin: 0;
}

.rich-content {
  padding: 0;
}

.content-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f2f5;
}

.content-section:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.section-title .el-icon {
  color: #409eff;
  font-size: 20px;
}

.section-content p {
  line-height: 1.8;
  color: #606266;
  font-size: 15px;
  margin: 0;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.highlight-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.highlight-content h5 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.highlight-content p {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.practical-info {
  background: #fafbfc;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  min-width: 100px;
}

.info-value {
  color: #606266;
  font-size: 14px;
  flex: 1;
}

.website-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  word-break: break-all;
}

.website-link:hover {
  color: #1e40af;
  text-decoration: underline;
}

.nearby-recommendations {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.nearby-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nearby-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateY(-1px);
}

.nearby-distance {
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.nearby-info h5 {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.nearby-info p {
  font-size: 13px;
  color: #606266;
  margin: 0;
  line-height: 1.4;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .highlights-grid {
    grid-template-columns: 1fr;
  }
  
  .nearby-recommendations {
    grid-template-columns: 1fr;
  }
  
  .info-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    min-width: auto;
  }
}

.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px;
}

@media (max-width: 768px) {
  .detail-header {
    height: 300px;
  }

  .overlay {
    padding: 20px;
  }

  .base-name {
    font-size: 24px;
  }

  .detail-body {
    padding: 20px 0;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>