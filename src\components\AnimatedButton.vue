<template>
  <button 
    :class="[
      'group relative rounded-full transition-all duration-300 ease-in-out',
      'hover:scale-110 active:scale-95 shadow-button hover:shadow-button-hover',
      size === 'small' ? 'w-10 h-10' : 'w-12 h-12',
      variant === 'primary' ? 'bg-primary-blue' : 'bg-white/95 border border-header-blue/30 backdrop-blur-10',
      variant === 'primary' ? 'hover:bg-primary-blue/90' : 'hover:bg-header-blue'
    ]"
    @click="$emit('click')"
  >
    <!-- 背景装饰 -->
    <div :class="[
      'absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300',
      variant === 'primary' ? 'bg-gradient-to-br from-white/20 to-transparent' : 'bg-gradient-to-br from-white/10 to-transparent'
    ]"></div>
    
    <!-- 图标 -->
    <Icon 
      :icon="icon" 
      :class="[
        'transition-all duration-300',
        size === 'small' ? 'w-5 h-5' : 'w-6 h-6',
        variant === 'primary' ? 'text-white' : 'text-header-blue group-hover:text-white',
        rotateOnHover ? 'group-hover:rotate-90' : 'group-hover:scale-110'
      ]" 
    />
    
    <!-- 脉冲效果 -->
    <div v-if="pulse" class="absolute inset-0 rounded-full bg-current opacity-20 animate-ping"></div>
  </button>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface Props {
  icon: string
  variant?: 'primary' | 'secondary'
  size?: 'small' | 'medium'
  pulse?: boolean
  rotateOnHover?: boolean
}

withDefaults(defineProps<Props>(), {
  variant: 'secondary',
  size: 'medium',
  pulse: false,
  rotateOnHover: false
})

defineEmits<{
  click: []
}>()
</script> 