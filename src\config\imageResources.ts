// 图片资源配置
// 包含各种革命历史场景的高质量图片资源

export interface ImageResource {
  url: string;
  alt: string;
  description: string;
  source: string;
  license: string;
  dimensions?: string;
}

// 黄洋界哨口相关图片资源（真实实地图片）
export const huangYangJieImages: ImageResource[] = [
  {
    url: '/images/huangyangjie/memorial-stone.jpg',
    alt: '黄洋界哨口纪念碑',
    description: '黄洋界哨口标志性纪念碑，毛主席亲笔题词',
    source: '实地拍摄',
    license: '项目使用',
    dimensions: '1920 × 1080'
  },
  {
    url: '/images/huangyangjie/mountain-clouds.jpg',
    alt: '井冈山云海景观',
    description: '壮观的井冈山云海景色，黄洋界哨口实景',
    source: '实地拍摄',
    license: '项目使用',
    dimensions: '1920 × 1080'
  },
  {
    url: '/images/huangyangjie/memorial-building.jpg',
    alt: '黄洋界哨口纪念建筑',
    description: '黄洋界哨口纪念建筑，具有重要历史意义',
    source: '实地拍摄',
    license: '项目使用',
    dimensions: '1920 × 1080'
  },
  {
    url: '/images/huangyangjie/revolutionary-site.jpg',
    alt: '革命历史建筑',
    description: '井冈山革命历史建筑，展现红色文化传统',
    source: '实地拍摄',
    license: '项目使用',
    dimensions: '1920 × 1080'
  },
  {
    url: '/images/huangyangjie/flag-scene.jpg',
    alt: '井冈山旗帜场景',
    description: '井冈山红色旗帜纪念场景，体现革命精神',
    source: '实地拍摄',
    license: '项目使用',
    dimensions: '1920 × 1080'
  },
  {
    url: '/images/huangyangjie/education-activity.jpg',
    alt: '思政教育活动',
    description: '黄洋界哨口思政教育学习活动现场',
    source: '实地拍摄',
    license: '项目使用',
    dimensions: '1920 × 1080'
  }
];

// 井冈山革命历史图片资源
export const jingGangShanImages: ImageResource[] = [
  {
    url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: '革命纪念建筑',
    description: '革命纪念建筑，展现红色文化传承',
    source: 'Unsplash',
    license: 'Unsplash License (免费商用)',
    dimensions: '2070 × 1380'
  },
  {
    url: 'https://images.unsplash.com/photo-**********-72479768bada?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2076&q=80',
    alt: '红色旗帜',
    description: '红色旗帜飘扬，象征革命精神',
    source: 'Unsplash',
    license: 'Unsplash License (免费商用)',
    dimensions: '2076 × 1384'
  },
  {
    url: 'https://images.unsplash.com/photo-1495020689067-958852a7765e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80',
    alt: '历史文献',
    description: '历史文献资料，记录革命历史',
    source: 'Unsplash',
    license: 'Unsplash License (免费商用)',
    dimensions: '2069 × 1379'
  }
];

// 思政教育主题图片资源
export const politicalEducationImages: ImageResource[] = [
  {
    url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: '学生学习场景',
    description: '学生学习场景，体现思政教育意义',
    source: 'Unsplash',
    license: 'Unsplash License (免费商用)',
    dimensions: '2070 × 1380'
  },
  {
    url: 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: '图书馆学习',
    description: '图书馆学习环境，营造学术氛围',
    source: 'Unsplash',
    license: 'Unsplash License (免费商用)',
    dimensions: '2070 × 1380'
  }
];

// 获取随机图片资源
export function getRandomImage(imageArray: ImageResource[]): ImageResource {
  const randomIndex = Math.floor(Math.random() * imageArray.length);
  return imageArray[randomIndex];
}

// 获取黄洋界哨口主题图片
export function getHuangYangJieImage(index?: number): ImageResource {
  if (index !== undefined && index < huangYangJieImages.length) {
    return huangYangJieImages[index];
  }
  return getRandomImage(huangYangJieImages);
}

// 获取井冈山主题图片
export function getJingGangShanImage(index?: number): ImageResource {
  if (index !== undefined && index < jingGangShanImages.length) {
    return jingGangShanImages[index];
  }
  return getRandomImage(jingGangShanImages);
}

// 根据基地名称获取相应图片
export function getImageByBaseName(baseName: string): ImageResource {
  if (baseName.includes('黄洋界') || baseName.includes('哨口')) {
    return getHuangYangJieImage();
  }
  if (baseName.includes('井冈山')) {
    return getJingGangShanImage();
  }
  return getRandomImage([...huangYangJieImages, ...jingGangShanImages, ...politicalEducationImages]);
}

// 导出所有图片资源
export const allImages = {
  huangYangJie: huangYangJieImages,
  jingGangShan: jingGangShanImages,
  politicalEducation: politicalEducationImages
}; 