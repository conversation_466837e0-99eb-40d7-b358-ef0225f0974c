// API配置文件
export const API_CONFIG = {
  // API基础URL - 可以通过环境变量配置
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  
  // 请求超时时间
  TIMEOUT: 10000,
  
  // API端点
  ENDPOINTS: {
    // 分类相关
    CATEGORY: {
      LIST: '/category/public/categoryList'
    },
    
    // 基地相关
    BASE: {
      LIST: '/base/public/baseInfoList'
    },
    
    // 区划相关
    AREA: {
      LIST: '/province/areaList'
    }
  },
  
  // 请求头配置
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

// 导出默认配置
export default API_CONFIG
