{"headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' *.tianditu.gov.cn; connect-src 'self' *.tianditu.gov.cn api.tianditu.gov.cn; img-src 'self' data: blob: *.tianditu.gov.cn; style-src 'self' 'unsafe-inline' *.tianditu.gov.cn; font-src 'self' data:;"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}], "rewrites": [{"source": "/(.*)", "destination": "/"}]}