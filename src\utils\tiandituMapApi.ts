// 天地图API工具函数
import { env } from '../config/env'

export interface TiandituMapApiConfig {
  tk: string // 天地图API密钥
  width?: number // 图片宽度，默认400
  height?: number // 图片高度，默认300
  zoom?: number // 缩放级别，默认15
}

// 地理编码响应接口
export interface GeocodeResponse {
  result: {
    location: {
      lon: number
      lat: number
    }
    addressComponent: {
      address: string
      region: string
      city: string
      district: string
      street: string
      streetNumber: string
    }
  }
  status: string
  msg: string
}

// 逆地理编码响应接口
export interface ReverseGeocodeResponse {
  result: {
    formatted_address: string
    addressComponent: {
      address: string
      region: string
      city: string
      district: string
      street: string
      streetNumber: string
    }
  }
  status: string
  msg: string
}

// 坐标接口
export interface Coordinate {
  lng: number
  lat: number
}

// 默认配置
const defaultConfig: Required<Omit<TiandituMapApiConfig, 'tk'>> = {
  width: 400,
  height: 300,
  zoom: 15
}

/**
 * 生成天地图静态地图图片URL
 * @param lat 纬度
 * @param lng 经度
 * @param config 配置参数
 * @returns 静态地图图片URL
 */
export function generateTiandituStaticMapUrl(
  lat: number,
  lng: number,
  config: TiandituMapApiConfig
): string {
  const { tk, width = defaultConfig.width, height = defaultConfig.height, zoom = defaultConfig.zoom } = config
  
  // 天地图静态地图API URL
  const baseUrl = 'https://api.tianditu.gov.cn/staticimage'
  
  // 构建参数
  const params = new URLSearchParams({
    tk,
    center: `${lng},${lat}`, // 天地图使用经度在前，纬度在后
    width: width.toString(),
    height: height.toString(),
    zoom: zoom.toString(),
    format: 'png',
    // 添加标记点
    markers: `${lng},${lat}`,
    markerStyles: 'red'
  })
  
  return `${baseUrl}?${params.toString()}`
}

/**
 * 生成基地位置的静态地图图片URL
 * @param lat 纬度
 * @param lng 经度
 * @param config 配置参数
 * @returns 静态地图图片URL
 */
export function generateBaseMapImageUrl(
  lat: number,
  lng: number,
  config: TiandituMapApiConfig
): string {
  return generateTiandituStaticMapUrl(lat, lng, {
    ...config,
    zoom: 16 // 基地地图使用更高的缩放级别以显示更多细节
  })
}

/**
 * 批量生成所有基地的地图图片URL
 * @param bases 基地数据数组
 * @param config 配置参数
 * @returns 更新后的基地数据数组
 */
export function generateAllBaseMapImages<T extends { name: string; coordinates: { lat: number; lng: number } }>(
  bases: T[],
  config: TiandituMapApiConfig
): (T & { imageUrl: string })[] {
  return bases.map(base => ({
    ...base,
    imageUrl: generateBaseMapImageUrl(
      base.coordinates.lat,
      base.coordinates.lng,
      config
    )
  }))
}

// 错误处理：当API调用失败时使用的默认图片
export const DEFAULT_BASE_IMAGE = 'https://images.pexels.com/photos/1770809/pexels-photo-1770809.jpeg?auto=compress&cs=tinysrgb&w=400&h=300'

/**
 * 安全的图片URL生成函数，包含错误处理
 * @param lat 纬度
 * @param lng 经度
 * @param config 配置参数
 * @returns 静态地图图片URL或默认图片URL
 */
export function safeGenerateTiandituStaticMapUrl(
  lat: number,
  lng: number,
  config: TiandituMapApiConfig
): string {
  try {
    // 验证坐标有效性
    if (!lat || !lng || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      console.warn('无效的坐标参数:', { lat, lng })
      return DEFAULT_BASE_IMAGE
    }
    
    // 验证API密钥
    if (!config.tk) {
      console.warn('缺少天地图API密钥')
      return DEFAULT_BASE_IMAGE
    }
    
    return generateTiandituStaticMapUrl(lat, lng, config)
  } catch (error) {
    console.error('生成天地图图片URL失败:', error)
    return DEFAULT_BASE_IMAGE
  }
}

/**
 * 地址转坐标（地理编码）
 * @param address 地址字符串
 * @param tk 天地图API密钥，可选，不传则使用环境变量
 * @returns Promise<Coordinate | null> 返回坐标或null
 */
export async function addressToCoordinate(
  address: string,
  tk?: string
): Promise<Coordinate | null> {
  try {
    // 使用传入的tk或环境变量中的tk
    const apiKey = tk || env.TIANDITU_MAP_TK
    
    if (!apiKey) {
      console.error('❌ 缺少天地图API密钥')
      return null
    }
    
    if (!address || address.trim() === '') {
      console.error('❌ 地址不能为空')
      return null
    }
    
    // 构建请求URL
    const baseUrl = 'http://api.tianditu.gov.cn/geocoder'
    const ds = JSON.stringify({
      keyWord: address.trim()
    })
    
    const url = `${baseUrl}?ds=${encodeURIComponent(ds)}&tk=${apiKey}`
    
    console.log('🔍 正在查询地址:', address)
    
    const response = await fetch(url)
    
    if (!response.ok) {
      console.error('❌ 网络请求失败:', response.status, response.statusText)
      return null
    }
    
    const data: GeocodeResponse = await response.json()
    
    if (data.status === '0' && data.result && data.result.location) {
      const { lon, lat } = data.result.location
      console.log('✅ 地址转坐标成功:', { address, lng: lon, lat })
      return { lng: lon, lat }
    } else {
      console.error('❌ 地理编码失败:', data.msg || '未知错误')
      return null
    }
  } catch (error) {
    console.error('❌ 地址转坐标异常:', error)
    return null
  }
}

/**
 * 坐标转地址（逆地理编码）
 * @param coordinate 坐标对象
 * @param tk 天地图API密钥，可选，不传则使用环境变量
 * @returns Promise<string | null> 返回地址字符串或null
 */
export async function coordinateToAddress(
  coordinate: Coordinate,
  tk?: string
): Promise<string | null> {
  try {
    // 使用传入的tk或环境变量中的tk
    const apiKey = tk || env.TIANDITU_MAP_TK
    
    if (!apiKey) {
      console.error('❌ 缺少天地图API密钥')
      return null
    }
    
    if (!coordinate || typeof coordinate.lng !== 'number' || typeof coordinate.lat !== 'number') {
      console.error('❌ 坐标参数无效')
      return null
    }
    
    // 验证坐标范围
    if (coordinate.lat < -90 || coordinate.lat > 90 || coordinate.lng < -180 || coordinate.lng > 180) {
      console.error('❌ 坐标超出有效范围')
      return null
    }
    
    // 构建请求URL
    const baseUrl = 'http://api.tianditu.gov.cn/geocoder'
    const postStr = JSON.stringify({
      lon: coordinate.lng,
      lat: coordinate.lat,
      ver: 1
    })
    
    const url = `${baseUrl}?postStr=${encodeURIComponent(postStr)}&type=geocode&tk=${apiKey}`
    
    console.log('🔍 正在查询坐标:', coordinate)
    
    const response = await fetch(url)
    
    if (!response.ok) {
      console.error('❌ 网络请求失败:', response.status, response.statusText)
      return null
    }
    
    const data: ReverseGeocodeResponse = await response.json()
    
    if (data.status === '0' && data.result && data.result.formatted_address) {
      const address = data.result.formatted_address
      console.log('✅ 坐标转地址成功:', { coordinate, address })
      return address
    } else {
      console.error('❌ 逆地理编码失败:', data.msg || '未知错误')
      return null
    }
  } catch (error) {
    console.error('❌ 坐标转地址异常:', error)
    return null
  }
}

/**
 * 批量地址转坐标
 * @param addresses 地址数组
 * @param tk 天地图API密钥，可选，不传则使用环境变量
 * @returns Promise<Array<{ address: string; coordinate: Coordinate | null }>>
 */
export async function batchAddressToCoordinate(
  addresses: string[],
  tk?: string
): Promise<Array<{ address: string; coordinate: Coordinate | null }>> {
  const results: Array<{ address: string; coordinate: Coordinate | null }> = []
  
  // 为避免过快请求，添加延迟
  for (const address of addresses) {
    const coordinate = await addressToCoordinate(address, tk)
    results.push({ address, coordinate })
    
    // 请求间隔100ms，避免API限制
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return results
}

/**
 * 批量坐标转地址
 * @param coordinates 坐标数组
 * @param tk 天地图API密钥，可选，不传则使用环境变量
 * @returns Promise<Array<{ coordinate: Coordinate; address: string | null }>>
 */
export async function batchCoordinateToAddress(
  coordinates: Coordinate[],
  tk?: string
): Promise<Array<{ coordinate: Coordinate; address: string | null }>> {
  const results: Array<{ coordinate: Coordinate; address: string | null }> = []
  
  // 为避免过快请求，添加延迟
  for (const coordinate of coordinates) {
    const address = await coordinateToAddress(coordinate, tk)
    results.push({ coordinate, address })
    
    // 请求间隔100ms，避免API限制
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return results
} 