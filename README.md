# 井冈山思政课实践基地平台

## 📋 项目简介

基于Vue 3 + TypeScript + 天地图API开发的思政课实践基地展示平台，专注于展示江西省井冈山地区的红色教育基地，为师生提供全面的基地信息、实践活动安排和教学资源。

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **地图服务**: 天地图API
- **样式框架**: Tailwind CSS
- **开发工具**: ESLint + Prettier

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   ├── MapView.vue     # 地图视图组件
│   ├── FilterBar.vue   # 筛选栏组件
│   ├── BaseCard.vue    # 基地卡片组件
│   └── AnimatedButton.vue # 动画按钮组件
├── views/              # 页面组件
│   ├── Home.vue        # 首页
│   ├── BaseDetail.vue  # 基地详情页
│   ├── PoliticalBaseDetail.vue # 思政基地详情页
│   ├── AdminDashboard.vue # 管理后台
│   ├── AdminLogin.vue  # 管理员登录
│   └── LowCodeEditor.vue # 低代码编辑器
├── config/             # 配置文件
│   ├── tiandituMapConfig.ts # 天地图配置
│   ├── imageResources.ts    # 图片资源配置
│   └── env.ts               # 环境变量配置
├── utils/              # 工具函数
│   └── tiandituMapApi.ts    # 天地图API工具
├── data/               # 数据文件
│   └── mockData.ts          # 模拟数据
├── types/              # 类型定义
│   └── index.ts             # 全局类型定义
└── assets/             # 静态资源
    └── vue.svg              # Vue Logo
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发环境

```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 预览生产版本

```bash
npm run preview
# 或
yarn preview
```

## 🗺️ 地图服务配置

项目使用天地图API提供地图服务，需要配置API密钥：

1. 在 `src/config/tiandituMapConfig.ts` 中配置您的天地图API密钥
2. 或创建 `.env` 文件并添加：
   ```
   VITE_TIANDITU_MAP_TK=your_api_key_here
   ```

## 📚 主要功能

- **基地展示**: 以地图形式展示井冈山地区的思政教育基地
- **筛选功能**: 支持按基地类型进行筛选
- **详情查看**: 提供基地的详细信息、联系方式和开放时间
- **导航功能**: 一键跳转到地图导航
- **移动端适配**: 响应式设计，支持移动设备
- **管理后台**: 支持基地信息的管理和编辑

## 🎯 核心特性

- **组件化开发**: 采用Vue 3组合式API，代码结构清晰
- **模块化设计**: 配置、工具、数据分离，便于维护
- **类型安全**: 全面使用TypeScript，提供完整的类型检查
- **响应式布局**: 支持桌面端和移动端的最佳体验
- **高性能**: 基于Vite构建，开发和构建速度极快

## 📝 开发规范

- 使用Vue 3组合式API
- 严格遵循TypeScript类型检查
- 组件命名采用PascalCase
- 工具函数采用camelCase
- 配置文件采用kebab-case

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情
