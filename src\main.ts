import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { createRouter, createWebHistory } from 'vue-router'
import './style.css'
import App from './App.vue'
import Home from './views/Home.vue'
import BaseDetail from './views/BaseDetail.vue'
import PoliticalBaseDetail from './views/PoliticalBaseDetail.vue'
import AdminLogin from './views/AdminLogin.vue'
import AdminDashboard from './views/AdminDashboard.vue'
import LowCodeEditor from './views/LowCodeEditor.vue'
import { preloadTiandituMapSDK } from './utils/tiandituMapLoader'

const routes = [
  { path: '/', component: Home },
  { path: '/base/:id', component: BaseDetail, props: true },
  { path: '/political-base/:id', component: PoliticalBaseDetail, props: true },
  { path: '/admin/login', component: AdminLogin },
  { path: '/admin/dashboard', component: AdminDashboard },
  { path: '/admin/lowcode-editor/:baseId?', component: LowCodeEditor, props: true },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

const app = createApp(App)

app.use(ElementPlus)
app.use(router)

// Register all icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')

// 预加载天地图SDK（使用环境变量）
preloadTiandituMapSDK().catch(error => {
  console.warn('⚠️ 天地图SDK预加载失败，将在使用时动态加载:', error)
})

// 开发环境下加载坐标更新工具
// 在浏览器控制台中使用 window.coordinateUpdater 对象访问
if (import.meta.env.DEV) {
  // 动态导入坐标更新工具，避免类型错误
  import('./scripts/updateCoordinates').then(() => {
    console.log('🛠️ 坐标更新工具已加载，可在控制台使用:')
    console.log('window.coordinateUpdater.updateAll() // 批量更新所有坐标')
    console.log('window.coordinateUpdater.testSingle("江西省井冈山市茨坪镇") // 测试单个地址')
  }).catch(error => {
    console.error('❌ 坐标更新工具加载失败:', error)
  })
}