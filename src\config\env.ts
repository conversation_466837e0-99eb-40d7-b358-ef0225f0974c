/**
 * 环境变量统一管理
 * 用于Vercel等云平台部署时的API Key管理
 */

// 环境变量类型定义
export interface EnvironmentConfig {
  // 百度地图API配置
  BAIDU_MAP_AK: string
  
  // 天地图API配置
  TIANDITU_MAP_TK: string
  
  // API基础URL配置
  API_BASE_URL: string
  
  // 其他可能的API Key（预留）
  // GOOGLE_ANALYTICS_ID?: string
  // SENTRY_DSN?: string
}

// 获取环境变量的辅助函数
function getEnvVar(key: string, defaultValue?: string): string {
  const value = import.meta.env[key] || defaultValue
  if (!value) {
    console.warn(`⚠️ 环境变量 ${key} 未配置`)
  }
  return value || ''
}

// 环境变量配置
export const env: EnvironmentConfig = {
  // 百度地图API密钥
  BAIDU_MAP_AK: getEnvVar('VITE_BAIDU_MAP_AK'),
  
  // 天地图API密钥
  TIANDITU_MAP_TK: getEnvVar('VITE_TIANDITU_MAP_TK'),
  
  // API基础URL
  API_BASE_URL: getEnvVar('VITE_API_BASE_URL', import.meta.env.DEV ? '/api' : 'http://120.55.15.172:8100'),
  
  // 未来可扩展的其他API配置
  // GOOGLE_ANALYTICS_ID: getEnvVar('VITE_GOOGLE_ANALYTICS_ID'),
  // SENTRY_DSN: getEnvVar('VITE_SENTRY_DSN'),
}

// 验证必要的环境变量是否已配置
export function validateEnvironment(): boolean {
  const requiredVars = [
    { key: 'VITE_BAIDU_MAP_AK', value: env.BAIDU_MAP_AK, name: '百度地图API密钥' },
    { key: 'VITE_TIANDITU_MAP_TK', value: env.TIANDITU_MAP_TK, name: '天地图API密钥' }
  ]
  
  let isValid = true
  const missingVars: string[] = []
  
  requiredVars.forEach(({ key, value, name }) => {
    if (!value || value.trim() === '') {
      isValid = false
      missingVars.push(`${name} (${key})}`)
      console.warn(`❌ 缺少必要的环境变量: ${key} - ${name}`)
    } else {
      console.log(`✅ ${name} 已配置`)
    }
  })
  
  if (!isValid) {
    console.group('🔧 环境变量配置指南')
    console.log('请配置以下环境变量:')
    missingVars.forEach(varName => console.log(`  - ${varName}`))
    console.groupEnd()
  }
  
  return isValid
}

// 开发环境检查
export function isDevelopment(): boolean {
  return import.meta.env.DEV
}

// 生产环境检查
export function isProduction(): boolean {
  return import.meta.env.PROD
}

// 获取当前环境名称
export function getEnvironment(): string {
  return import.meta.env.MODE
}

// 调试信息（仅在开发环境显示）
if (isDevelopment()) {
  console.group('🌍 环境配置信息')
  console.log(`当前环境: ${getEnvironment()}`)
  console.log(`API基础URL: ${env.API_BASE_URL}`)
  console.log(`百度地图AK: ${env.BAIDU_MAP_AK ? '已配置' : '未配置'}`)
  console.log(`天地图TK: ${env.TIANDITU_MAP_TK ? '已配置' : '未配置'}`)
  console.groupEnd()
  
  // 验证环境变量
  validateEnvironment()
} 