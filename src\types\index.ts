export interface PracticeBase {
  id: string
  name: string
  city: string
  category: string
  address: string
  description: string
  phone?: string
  openHours?: string
  imageUrl: string
  features?: string[]
  rating: number
  coordinates: {
    lat: number
    lng: number
  }
  websiteUrl?: string // 官方网站链接（可选）
  
  // 新增思政课实践基地相关字段
  virtualHallUrl?: string // 虚拟展馆链接
  contactPerson?: string // 联系人
  suggestedTimeSlots?: string[] // 建议时间段
  suggestedCapacity?: number // 建议课程容量
  practiceActivities?: PracticeActivity[] // 实践活动
  demonstrationCourses?: DemonstrationCourse[] // 示范课程
  demonstrationPlans?: DemonstrationPlan[] // 示范教案
  specialImages?: string[] // 特色实景图片
  baseIntroduction?: string // 基地简介（详细版）
}

export interface PracticeActivity {
  id: string
  title: string
  description: string
  duration: string
  capacity: number
  imageUrl?: string
}

export interface DemonstrationCourse {
  id: string
  title: string
  instructor: string
  duration: string
  description: string
  tags: string[]
  videoUrl?: string
  downloadUrl?: string
}

export interface DemonstrationPlan {
  id: string
  title: string
  subject: string
  grade: string
  author: string
  description: string
  downloadUrl?: string
}

export interface FilterOptions {
  city: string
  category: string
  search: string
}

// 百度地图相关类型声明
declare global {
  interface Window {
    BMap: any
    BMAP_NORMAL_MAP: any
    BMAP_ANIMATION_BOUNCE: any
    viewBaseDetail: (baseId: string) => void
  }
}