<template>
  <div class="political-base-detail">
    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="hero-background">
        <img :src="heroBackgroundImage.url" :alt="heroBackgroundImage.alt" />
        <div class="hero-overlay"></div>
      </div>
      
      <div class="hero-content">
        <div class="navigation">
          <el-button @click="goBack" :icon="ArrowLeft" circle class="back-btn" />
          <div class="nav-breadcrumb">
            <Icon icon="material-symbols:home" class="breadcrumb-icon" />
            <span>首页</span>
            <Icon icon="material-symbols:chevron-right" class="breadcrumb-divider" />
            <span>思政基地</span>
            <Icon icon="material-symbols:chevron-right" class="breadcrumb-divider" />
            <span>{{ currentBase?.name || '加载中...' }}</span>
          </div>
        </div>
        
        <div v-if="!loading && currentBase" class="hero-info">
          <div class="hero-badge">
            <Icon icon="material-symbols:star" class="badge-icon" />
            <span>思政教育基地</span>
          </div>
          
          <h1 class="hero-title">{{ currentBase.name }}</h1>
          <p class="hero-subtitle">江西省学校"大思政课"实践教学基地</p>
          
          <div class="hero-stats">
            <div class="stat-item">
              <Icon icon="material-symbols:group" class="stat-icon" />
              <div class="stat-info">
                <div class="stat-value">{{ currentBase.suggestedCapacity || 50 }}</div>
                <div class="stat-label">建议容量</div>
              </div>
            </div>
            <div class="stat-item">
              <Icon icon="material-symbols:schedule" class="stat-icon" />
              <div class="stat-info">
                <div class="stat-value">{{ currentBase.suggestedTimeSlots?.length || 2 }}</div>
                <div class="stat-label">时间段</div>
              </div>
            </div>
            <div class="stat-item">
              <Icon icon="material-symbols:star-rate" class="stat-icon" />
              <div class="stat-info">
                <div class="stat-value">{{ currentBase.rating || 5.0 }}</div>
                <div class="stat-label">评分</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-section">
      <el-loading v-loading="loading" element-loading-text="正在加载基地信息..." />
    </div>

    <!-- 主要内容区域 -->
    <div v-else-if="currentBase" class="main-content">
      <div class="container">
        <!-- 虚拟展馆 -->
        <div class="section virtual-hall">
          <div class="section-header">
            <Icon icon="material-symbols:view-in-ar" class="section-icon" />
            <h3 class="section-title">虚拟展馆</h3>
            <div class="section-badge">VR体验</div>
          </div>
          <div class="virtual-hall-content">
            <div v-if="iframeLoading" class="iframe-loading">
              <el-loading v-loading="iframeLoading" element-loading-text="正在加载虚拟展馆..." />
            </div>
            <div v-if="iframeError" class="iframe-error">
              <el-alert title="虚拟展馆加载失败" type="error" show-icon />
              <el-button @click="retryIframe" type="primary" style="margin-top: 10px;">重新加载</el-button>
            </div>
            <iframe
              v-if="currentBase.virtualHallUrl"
              :src="currentBase.virtualHallUrl"
              class="virtual-hall-iframe"
              @load="onIframeLoad"
              @error="onIframeError"
            />
            <div v-else class="no-virtual-hall">
              <Icon icon="material-symbols:view-in-ar" class="empty-icon" />
              <h4>暂无虚拟展馆</h4>
              <p>敬请期待，我们正在为您准备精彩的虚拟展馆体验</p>
            </div>
          </div>
        </div>

        <!-- 基地信息卡片 -->
        <div class="info-cards">
          <div class="left-column">
            <!-- 基地简介 -->
            <div class="section base-intro">
              <div class="section-header">
                <Icon icon="material-symbols:info" class="section-icon" />
                <h3 class="section-title">基地简介</h3>
              </div>
              <div class="base-intro-content">
                <p>{{ currentBase.baseIntroduction || currentBase.description }}</p>
              </div>
            </div>

            <!-- 基地信息 -->
            <div class="section base-info">
              <div class="section-header">
                <Icon icon="material-symbols:location-on" class="section-icon" />
                <h3 class="section-title">基地信息</h3>
              </div>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-icon">
                    <Icon icon="material-symbols:location-on" />
                  </div>
                  <div class="info-content">
                    <div class="info-label">基地地址</div>
                    <div class="info-value">{{ currentBase.address }}</div>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <Icon icon="material-symbols:person" />
                  </div>
                  <div class="info-content">
                    <div class="info-label">联系人</div>
                    <div class="info-value">{{ currentBase.contactPerson || '暂无' }}</div>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <Icon icon="material-symbols:call" />
                  </div>
                  <div class="info-content">
                    <div class="info-label">联系电话</div>
                    <div class="info-value">
                      <a :href="`tel:${currentBase.phone}`" class="phone-link">{{ currentBase.phone }}</a>
                    </div>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <Icon icon="material-symbols:schedule" />
                  </div>
                  <div class="info-content">
                    <div class="info-label">开放时间</div>
                    <div class="info-value">{{ currentBase.openHours }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 建议时间段 -->
            <div class="section suggested-time">
              <div class="section-header">
                <Icon icon="material-symbols:schedule" class="section-icon" />
                <h3 class="section-title">建议时间段</h3>
              </div>
              <div class="time-slots">
                <div
                  v-for="slot in currentBase.suggestedTimeSlots"
                  :key="slot"
                  class="time-slot"
                >
                  <Icon icon="material-symbols:access-time" class="slot-icon" />
                  <span>{{ slot }}</span>
                </div>
              </div>
            </div>

            <!-- 建议课程容量 -->
            <div class="section suggested-capacity">
              <div class="section-header">
                <Icon icon="material-symbols:group" class="section-icon" />
                <h3 class="section-title">建议课程容量</h3>
              </div>
              <div class="capacity-info">
                <div class="capacity-card">
                  <div class="capacity-number">{{ currentBase.suggestedCapacity || 50 }}</div>
                  <div class="capacity-label">建议人数</div>
                  <div class="capacity-unit">人</div>
                </div>
              </div>
            </div>
          </div>

          <div class="right-column">
            <!-- 实践基地特色实景图 -->
            <div class="section special-images">
              <div class="section-header">
                <Icon icon="material-symbols:photo-camera" class="section-icon" />
                <h3 class="section-title">实践基地特色实景图</h3>
              </div>
              <div class="images-gallery">
                <div
                  v-for="(image, index) in currentBase.specialImages"
                  :key="index"
                  class="gallery-item"
                >
                  <el-image
                    :src="image"
                    :preview-src-list="currentBase.specialImages"
                    :initial-index="index"
                    class="gallery-image"
                    fit="cover"
                  />
                  <div class="gallery-overlay">
                    <Icon icon="material-symbols:zoom-in" class="overlay-icon" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实践活动 -->
        <div class="section practice-activities">
          <div class="section-header">
            <Icon icon="material-symbols:event" class="section-icon" />
            <h3 class="section-title">实践活动</h3>
            <div class="section-badge">体验式学习</div>
            <el-button v-if="showMoreActivities" @click="toggleActivities" type="primary" link>
              {{ expandedActivities ? '收起' : '展开更多' }}
            </el-button>
          </div>
          <div class="activities-grid">
            <div
              v-for="activity in displayedActivities"
              :key="activity.id"
              class="activity-card"
            >
              <div class="activity-image">
                <img :src="activity.imageUrl" :alt="activity.title" />
                <div class="activity-badge">
                  <Icon icon="material-symbols:star" />
                  <span>热门</span>
                </div>
              </div>
              <div class="activity-content">
                <h4 class="activity-title">{{ activity.title }}</h4>
                <p class="activity-desc">{{ activity.description }}</p>
                <div class="activity-meta">
                  <div class="meta-item">
                    <Icon icon="material-symbols:schedule" class="meta-icon" />
                    <span>{{ activity.duration }}</span>
                  </div>
                  <div class="meta-item">
                    <Icon icon="material-symbols:group" class="meta-icon" />
                    <span>{{ activity.capacity }}人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 示范课程 -->
        <div class="section demonstration-courses">
          <div class="section-header">
            <Icon icon="material-symbols:school" class="section-icon" />
            <h3 class="section-title">示范课程</h3>
            <div class="section-badge">精品课程</div>
            <el-button v-if="showMoreCourses" @click="toggleCourses" type="primary" link>
              {{ expandedCourses ? '收起' : '展开更多' }}
            </el-button>
          </div>
          <div class="courses-list">
            <div
              v-for="course in displayedCourses"
              :key="course.id"
              class="course-card"
            >
              <div class="course-header">
                <div class="course-icon">
                  <Icon icon="material-symbols:play-circle" />
                </div>
                <div class="course-info">
                  <h4 class="course-title">{{ course.title }}</h4>
                  <div class="course-meta">
                    <span class="course-instructor">
                      <Icon icon="material-symbols:person" />
                      {{ course.instructor }}
                    </span>
                    <span class="course-duration">
                      <Icon icon="material-symbols:schedule" />
                      {{ course.duration }}
                    </span>
                  </div>
                </div>
                <div class="course-actions">
                  <el-button v-if="course.videoUrl" @click="playVideo(course)" type="primary" size="small">
                    <Icon icon="material-symbols:play-arrow" />
                    观看视频
                  </el-button>
                  <el-button v-if="course.downloadUrl" @click="downloadCourse(course)" type="success" size="small">
                    <Icon icon="material-symbols:download" />
                    下载课件
                  </el-button>
                </div>
              </div>
              <p class="course-description">{{ course.description }}</p>
            </div>
          </div>
        </div>

        <!-- 示范教案 -->
        <div class="section demonstration-plans">
          <div class="section-header">
            <Icon icon="material-symbols:description" class="section-icon" />
            <h3 class="section-title">示范教案</h3>
            <div class="section-badge">教学资源</div>
            <el-button v-if="showMorePlans" @click="togglePlans" type="primary" link>
              {{ expandedPlans ? '收起' : '展开更多' }}
            </el-button>
          </div>
          <div class="plans-list">
            <div
              v-for="plan in displayedPlans"
              :key="plan.id"
              class="plan-card"
            >
              <div class="plan-header">
                <div class="plan-icon">
                  <Icon icon="material-symbols:description" />
                </div>
                <div class="plan-info">
                  <h4 class="plan-title">{{ plan.title }}</h4>
                  <div class="plan-meta">
                    <span class="plan-subject">
                      <Icon icon="material-symbols:book" />
                      {{ plan.subject }}
                    </span>
                    <span class="plan-grade">
                      <Icon icon="material-symbols:school" />
                      {{ plan.grade }}
                    </span>
                    <span class="plan-author">
                      <Icon icon="material-symbols:person" />
                      {{ plan.author }}
                    </span>
                  </div>
                </div>
                <el-button @click="downloadPlan(plan)" type="success" size="small">
                  <Icon icon="material-symbols:download" />
                  下载教案
                </el-button>
              </div>
              <p class="plan-description">{{ plan.description }}</p>
            </div>
          </div>
        </div>

        <!-- 百度地图 -->
        <div class="section map-section">
          <div class="section-header">
            <Icon icon="material-symbols:map" class="section-icon" />
            <h3 class="section-title">基地位置</h3>
            <div class="section-badge">导航定位</div>
          </div>
          <div class="map-wrapper">
            <div ref="mapContainer" class="map-container"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <div class="error-content">
        <Icon icon="material-symbols:error" class="error-icon" />
        <h3>基地信息加载失败</h3>
        <p>抱歉，无法加载基地信息，请稍后重试</p>
        <el-button @click="loadBaseInfo" type="primary">
          <Icon icon="material-symbols:refresh" />
          重新加载
        </el-button>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="footer">
      <div class="footer-content">
        <div class="footer-logo">
          <Icon icon="material-symbols:school" class="logo-icon" />
          <span>思政教学基地</span>
        </div>
        <p>版权所有 © 2016 - 2025 江西省学校"大思政课"实践教学基地</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { mockBases } from '../data/mockData'
import type { PracticeBase } from '../types'
import { env } from '../config/env'
import { getImageByBaseName } from '../config/imageResources'

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const currentBase = ref<PracticeBase | null>(null)
const mapContainer = ref<HTMLElement>()
const iframeLoading = ref(true)
const iframeError = ref(false)
const expandedActivities = ref(false)
const expandedCourses = ref(false)
const expandedPlans = ref(false)
let detailMap: any = null

const baseId = computed(() => route.params.id as string)

// 动态选择英雄背景图片
const heroBackgroundImage = computed(() => {
  if (!currentBase.value) {
    return {
      url: 'https://images.unsplash.com/photo-1553729459-efe14ef6055d?auto=format&fit=crop&w=1920&h=600&q=80',
      alt: '红色文化背景'
    }
  }
  return getImageByBaseName(currentBase.value.name)
})

// 控制显示的活动数量
const displayedActivities = computed(() => {
  const activities = currentBase.value?.practiceActivities || []
  return expandedActivities.value ? activities : activities.slice(0, 3)
})

const showMoreActivities = computed(() => {
  return (currentBase.value?.practiceActivities?.length || 0) > 3
})

// 控制显示的课程数量
const displayedCourses = computed(() => {
  const courses = currentBase.value?.demonstrationCourses || []
  return expandedCourses.value ? courses : courses.slice(0, 2)
})

const showMoreCourses = computed(() => {
  return (currentBase.value?.demonstrationCourses?.length || 0) > 2
})

// 控制显示的教案数量
const displayedPlans = computed(() => {
  const plans = currentBase.value?.demonstrationPlans || []
  return expandedPlans.value ? plans : plans.slice(0, 2)
})

const showMorePlans = computed(() => {
  return (currentBase.value?.demonstrationPlans?.length || 0) > 2
})

const goBack = () => {
  router.go(-1)
}

const loadBaseInfo = async () => {
  try {
    loading.value = true
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const base = mockBases.find(b => b.id === baseId.value)
    if (base) {
      currentBase.value = base
      await initDetailMap()
    }
  } catch (error) {
    console.error('加载基地信息失败:', error)
  } finally {
    loading.value = false
  }
}

const onIframeLoad = () => {
  iframeLoading.value = false
  iframeError.value = false
}

const onIframeError = () => {
  iframeLoading.value = false
  iframeError.value = true
}

const retryIframe = () => {
  iframeLoading.value = true
  iframeError.value = false
  const iframe = document.querySelector('.virtual-hall-iframe') as HTMLIFrameElement
  if (iframe) {
    iframe.src = iframe.src
  }
}

const toggleActivities = () => {
  expandedActivities.value = !expandedActivities.value
}

const toggleCourses = () => {
  expandedCourses.value = !expandedCourses.value
}

const togglePlans = () => {
  expandedPlans.value = !expandedPlans.value
}

const playVideo = (course: any) => {
  window.open(course.videoUrl, '_blank')
}

const downloadCourse = (course: any) => {
  window.open(course.downloadUrl, '_blank')
}

const downloadPlan = (plan: any) => {
  window.open(plan.downloadUrl, '_blank')
}

// 创建基地类别对应的标记图标
const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    '习近平总书记足迹': '#1e40af',
    '井冈山精神': '#dc2626', 
    '红色旅游': '#ea580c',
    '革命遗址': '#b91c1c',
    '英烈纪念': '#7c3aed'
  }
  return colors[category] || '#dc2626'
}

const createCustomMarkerIcon = (category: string) => {
  const color = getCategoryColor(category)
  return new (window as any).BMapGL.Icon(
    `data:image/svg+xml;base64,${btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="50" viewBox="0 0 40 50">
        <defs>
          <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
          </filter>
          <linearGradient id="grad" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${color};stop-opacity:0.8" />
          </linearGradient>
        </defs>
        <path d="M20 5C12.3 5 6 11.3 6 19c0 12 14 26 14 26s14-14 14-26C34 11.3 27.7 5 20 5z" 
              fill="url(#grad)" filter="url(#shadow)"/>
        <circle cx="20" cy="19" r="8" fill="white" opacity="0.9"/>
        <circle cx="20" cy="19" r="4" fill="${color}"/>
        <path d="M16 19l2 2 4-4" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `)}`,
    new (window as any).BMapGL.Size(40, 50),
    {
      anchor: new (window as any).BMapGL.Size(20, 50)
    }
  )
}

// 创建详细的信息窗口内容
const createDetailedInfoWindow = (base: PracticeBase) => {
  return `
    <div style="padding: 20px; max-width: 380px; background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); border-radius: 12px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);">
      <!-- 标题区域 -->
      <div style="margin-bottom: 16px; text-align: center;">
        <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px; font-weight: 700; line-height: 1.3;">${base.name}</h3>
        <div style="display: flex; justify-content: center; gap: 8px; margin-bottom: 12px;">
          <span style="display: inline-block; background: ${getCategoryColor(base.category)}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;">
            ${base.category}
          </span>
          <span style="display: inline-flex; align-items: center; gap: 4px; background: #fbbf24; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;">
            ⭐ ${base.rating || 5.0}
          </span>
        </div>
      </div>
      
      <!-- 基地信息卡片 -->
      <div style="background: white; padding: 16px; border-radius: 8px; border: 1px solid #e5e7eb; margin-bottom: 16px;">
        <div style="display: flex; align-items: flex-start; gap: 8px; margin-bottom: 12px;">
          <span style="color: ${getCategoryColor(base.category)}; font-size: 16px; margin-top: 2px;">📍</span>
          <div>
            <p style="margin: 0 0 6px 0; color: #374151; font-size: 14px; font-weight: 500; line-height: 1.4;">${base.address}</p>
          </div>
        </div>
        
        ${base.phone ? `
        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
          <span style="color: ${getCategoryColor(base.category)}; font-size: 16px;">📞</span>
          <a href="tel:${base.phone}" style="color: #2563eb; text-decoration: none; font-size: 14px; font-weight: 500;">${base.phone}</a>
        </div>
        ` : ''}
        
        ${base.openHours ? `
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="color: ${getCategoryColor(base.category)}; font-size: 16px;">🕒</span>
          <span style="color: #6b7280; font-size: 13px;">${base.openHours}</span>
        </div>
        ` : ''}
      </div>
      
      <!-- 操作按钮区域 -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
        <button onclick="window.openNavigation('${base.coordinates.lat}', '${base.coordinates.lng}', '${base.name}')" 
                style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; border: none; padding: 10px 16px; border-radius: 6px; font-size: 13px; font-weight: 500; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 6px; transition: all 0.2s ease;"
                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(37, 99, 235, 0.3)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
          <span>🧭</span>
          <span>导航</span>
        </button>
        
        <button onclick="window.viewBaseDetail()" 
                style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; border: none; padding: 10px 16px; border-radius: 6px; font-size: 13px; font-weight: 500; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 6px; transition: all 0.2s ease;"
                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(220, 38, 38, 0.3)';"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
          <span>📖</span>
          <span>详情</span>
        </button>
      </div>
    </div>
  `
}

const initDetailMap = async () => {
  if (!mapContainer.value || !currentBase.value) {
    return
  }

  try {
    if (!(window as any).BMapGL) {
      await new Promise((resolve) => {
        const script = document.createElement('script')
        script.src = `https://api.map.baidu.com/api?v=1.0&type=webgl&library=CoordConverter&ak=${env.BAIDU_MAP_AK}`
        script.onload = resolve
        document.head.appendChild(script)
      })
    }

    // 验证坐标转换功能是否可用
    if (!(window as any).BMapGL.Convertor) {
      console.error('⚠️ 百度地图坐标转换库(Convertor)未加载，请检查API加载URL')
      console.log('💡 当前API URL应包含 &library=CoordConverter 参数')
    } else {
      console.log('✅ 百度地图坐标转换库加载成功')
    }

    // 创建地图实例
    detailMap = new (window as any).BMapGL.Map(mapContainer.value)
    
    // 原始坐标点（可能是WGS-84或GCJ-02坐标系）
    const originalPoint = new (window as any).BMapGL.Point(
      currentBase.value.coordinates.lng, 
      currentBase.value.coordinates.lat
    )
    
    console.log(`开始为 ${currentBase.value.name} 进行坐标转换`)
    
    // 使用百度地图官方坐标转换API
    if ((window as any).BMapGL.Convertor) {
      const convertor = new (window as any).BMapGL.Convertor()
      const pointsToConvert = [originalPoint]
      
      // 转换坐标：从WGS-84转换到BD-09
      convertor.translate(pointsToConvert, 1, 5, (result: any) => {
        let convertedPoint = originalPoint
        
        if (result.status === 0) {
          convertedPoint = result.points[0]
          console.log(`✅ ${currentBase.value!.name} WGS-84坐标转换成功`)
        } else {
          console.warn(`⚠️ ${currentBase.value!.name} WGS-84坐标转换失败，尝试GCJ-02转换...`)
          
          // 尝试从GCJ-02转换到BD-09
          convertor.translate([originalPoint], 3, 5, (result2: any) => {
            if (result2.status === 0) {
              convertedPoint = result2.points[0]
              console.log(`✅ ${currentBase.value!.name} GCJ-02坐标转换成功`)
            } else {
              console.warn(`⚠️ ${currentBase.value!.name} 坐标转换失败，使用原始坐标`)
              convertedPoint = originalPoint
            }
            
            createMapWithPoint(convertedPoint)
          })
          return // 避免重复创建地图
        }
        
        createMapWithPoint(convertedPoint)
      })
    } else {
      console.warn('⚠️ 百度地图坐标转换功能不可用，使用原始坐标')
      createMapWithPoint(originalPoint)
    }
    
  } catch (error) {
    console.error('地图初始化失败:', error)
  }
  
  // 创建地图的辅助函数
  function createMapWithPoint(point: any) {
    try {
      // 设置地图中心和缩放级别
      detailMap.centerAndZoom(point, 16)
      detailMap.enableScrollWheelZoom(true)
      
      // 添加地图控件（WebGL版本支持的控件）
      detailMap.addControl(new (window as any).BMapGL.ZoomControl({
        anchor: (window as any).BMAP_ANCHOR_TOP_LEFT
      }))
      detailMap.addControl(new (window as any).BMapGL.ScaleControl({
        anchor: (window as any).BMAP_ANCHOR_BOTTOM_LEFT
      }))
      detailMap.addControl(new (window as any).BMapGL.MapTypeControl({
        anchor: (window as any).BMAP_ANCHOR_TOP_RIGHT
      }))
      
      // 创建自定义标记
      const customIcon = createCustomMarkerIcon(currentBase.value!.category)
      const marker = new (window as any).BMapGL.Marker(point, { icon: customIcon })
      detailMap.addOverlay(marker)
      
      // 添加标记动画效果
      marker.setAnimation((window as any).BMAP_ANIMATION_BOUNCE)
      setTimeout(() => {
        marker.setAnimation(null)
      }, 3000)
      
      // 创建详细信息窗口
      const infoWindowContent = createDetailedInfoWindow(currentBase.value!)
      const infoWindow = new (window as any).BMapGL.InfoWindow(infoWindowContent, {
        width: 380,
        height: 280,
        enableCloseOnClick: false,
        offset: new (window as any).BMapGL.Size(0, -50)
      })
      
      // 标记点击事件
      marker.addEventListener('click', () => {
        detailMap.openInfoWindow(infoWindow, point)
      })
      
      // 添加周边基地标记（可选）
      addNearbyBases(point)
      
      // 添加路线规划按钮
      addRouteButton()
      
      // 自动打开信息窗口
      setTimeout(() => {
        detailMap.openInfoWindow(infoWindow, point)
      }, 1000)
      
    } catch (error) {
      console.error('❌ 创建地图失败:', error)
    }
  }
}

// 添加周边基地标记
const addNearbyBases = (centerPoint: any) => {
  const currentId = currentBase.value?.id
  const nearbyBases = mockBases.filter(base => 
    base.id !== currentId && 
    calculateDistance(
      centerPoint.lat, centerPoint.lng,
      base.coordinates.lat, base.coordinates.lng
    ) < 10 // 10公里内
  ).slice(0, 3) // 最多显示3个

  // 为每个周边基地进行坐标转换并添加标记
  nearbyBases.forEach(base => {
    const originalPoint = new (window as any).BMapGL.Point(base.coordinates.lng, base.coordinates.lat)
    
    // 使用百度地图官方坐标转换API
    if ((window as any).BMapGL && (window as any).BMapGL.Convertor) {
      const convertor = new (window as any).BMapGL.Convertor()
      
      // 转换周边基地坐标
      convertor.translate([originalPoint], 1, 5, (result: any) => {
        let pointToUse = originalPoint
        
        if (result.status === 0) {
          pointToUse = result.points[0]
          console.log(`✅ ${base.name} WGS-84坐标转换成功`)
        } else {
          // 尝试GCJ-02转换
          convertor.translate([originalPoint], 3, 5, (result2: any) => {
            if (result2.status === 0) {
              pointToUse = result2.points[0]
              console.log(`✅ ${base.name} GCJ-02坐标转换成功`)
            } else {
              console.warn(`⚠️ ${base.name} 坐标转换失败，使用原始坐标`)
              pointToUse = originalPoint
            }
            
            createNearbyMarker(base, pointToUse)
          })
          return // 避免重复创建标记
        }
        
        createNearbyMarker(base, pointToUse)
      })
    } else {
      console.warn('⚠️ 百度地图坐标转换功能不可用，使用原始坐标')
      createNearbyMarker(base, originalPoint)
    }
  })
  
  // 创建周边基地标记的辅助函数
  function createNearbyMarker(base: any, point: any) {
    const icon = new (window as any).BMapGL.Icon(
      `data:image/svg+xml;base64,${btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="30" viewBox="0 0 24 30">
          <path d="M12 3C8.7 3 6 5.7 6 9c0 6 6 15 6 15s6-9 6-15C18 5.7 15.3 3 12 3z" 
                fill="#6b7280" opacity="0.8"/>
          <circle cx="12" cy="9" r="4" fill="white"/>
          <circle cx="12" cy="9" r="2" fill="#6b7280"/>
        </svg>
      `)}`,
      new (window as any).BMapGL.Size(24, 30),
      { anchor: new (window as any).BMapGL.Size(12, 30) }
    )
    
    const marker = new (window as any).BMapGL.Marker(point, { icon })
    detailMap.addOverlay(marker)
    
    const label = new (window as any).BMapGL.Label(base.name, {
      offset: new (window as any).BMapGL.Size(0, -35)
    })
    label.setStyle({
      color: '#4b5563',
      fontSize: '12px',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      border: '1px solid #d1d5db',
      borderRadius: '4px',
      padding: '2px 6px'
    })
    marker.setLabel(label)
  }
}

// 计算两点之间的距离（简化版本）
const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
  const R = 6371 // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) + 
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
            Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// 添加路线规划按钮
const addRouteButton = () => {
  // 注册全局函数供信息窗口调用
  ;(window as any).openNavigation = (lat: string, lng: string, name: string) => {
    // 打开百度地图导航
    const url = `https://map.baidu.com/dir?destination=${lat},${lng}&destination_name=${encodeURIComponent(name)}&mode=driving&src=webapp.baidu.openapi`
    window.open(url, '_blank')
  }
  
  ;(window as any).viewBaseDetail = () => {
    // 已经在当前页面，滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

onMounted(() => {
  loadBaseInfo()
})
</script>

<style scoped>
.political-base-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #fefefe 0%, #f8f9fa 100%);
}

/* 英雄区域 */
.hero-section {
  position: relative;
  height: 500px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(220, 38, 38, 0.85) 0%, rgba(185, 28, 28, 0.9) 100%);
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.nav-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.breadcrumb-icon, .breadcrumb-divider {
  color: rgba(255, 255, 255, 0.7);
}

.hero-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  color: white;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 500;
  margin: 0 auto 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-icon {
  color: #fbbf24;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 20px;
  margin: 0 0 40px 0;
  opacity: 0.9;
  font-weight: 300;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  font-size: 32px;
  color: #fbbf24;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 加载状态 */
.loading-section {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 主要内容区域 */
.main-content {
  padding: 60px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.section {
  margin-bottom: 60px;
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  position: relative;
}

.section-icon {
  font-size: 24px;
  color: #dc2626;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.section-badge {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-left: auto;
}

/* 虚拟展馆 */
.virtual-hall-content {
  border-radius: 12px;
  overflow: hidden;
  background: #f9fafb;
}

.virtual-hall-iframe {
  width: 100%;
  height: 500px;
  border: none;
  border-radius: 12px;
}

.no-virtual-hall {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 64px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.no-virtual-hall h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #374151;
}

.no-virtual-hall p {
  margin: 0;
  font-size: 14px;
}

/* 信息卡片布局 */
.info-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 60px;
}

.left-column, .right-column {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 基地简介 */
.base-intro-content {
  font-size: 16px;
  line-height: 1.7;
  color: #374151;
}

/* 基地信息 */
.info-grid {
  display: grid;
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: #f3f4f6;
  border-color: #dc2626;
}

.info-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  border-radius: 10px;
  font-size: 18px;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}

.phone-link {
  color: #dc2626;
  text-decoration: none;
  transition: color 0.3s ease;
}

.phone-link:hover {
  color: #b91c1c;
}

/* 建议时间段 */
.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.time-slot {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #fca5a5;
  border-radius: 12px;
  color: #991b1b;
  font-weight: 500;
  transition: all 0.3s ease;
}

.time-slot:hover {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  transform: translateY(-2px);
}

.slot-icon {
  font-size: 20px;
  color: #dc2626;
}

/* 建议容量 */
.capacity-card {
  text-align: center;
  padding: 32px;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.capacity-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

.capacity-number {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
}

.capacity-label {
  font-size: 16px;
  margin-bottom: 4px;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.capacity-unit {
  font-size: 14px;
  opacity: 0.7;
  position: relative;
  z-index: 1;
}

/* 特色图片 */
.images-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.gallery-item {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  aspect-ratio: 16/9;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: scale(1.05);
}

.gallery-image {
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.overlay-icon {
  font-size: 32px;
  color: white;
}

/* 活动网格 */
.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.activity-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.activity-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.activity-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.activity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-image img {
  transform: scale(1.1);
}

.activity-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.activity-content {
  padding: 24px;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.activity-desc {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.activity-meta {
  display: flex;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.meta-icon {
  font-size: 16px;
  color: #dc2626;
}

/* 课程和教案列表 */
.courses-list, .plans-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.course-card, .plan-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.course-card:hover, .plan-card:hover {
  background: white;
  border-color: #dc2626;
  transform: translateY(-2px);
}

.course-header, .plan-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.course-icon, .plan-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  border-radius: 12px;
  font-size: 20px;
}

.course-info, .plan-info {
  flex: 1;
}

.course-title, .plan-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.course-meta, .plan-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6b7280;
}

.course-meta > span, .plan-meta > span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.course-actions {
  display: flex;
  gap: 8px;
}

.course-description, .plan-description {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* 地图区域 */
.map-section {
  position: relative;
  overflow: visible;
}

.map-wrapper {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.map-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #dc2626;
}

.map-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
  z-index: 1;
}

.map-container {
  width: 100%;
  height: 500px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  position: relative;
  z-index: 0;
}

/* 地图加载状态 */
.map-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #dc2626;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 地图控件样式优化 */
.map-container :global(.BMap_stdMpPan) {
  border-radius: 8px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.map-container :global(.BMap_stdMpType2) {
  border-radius: 8px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.map-container :global(.BMap_stdMpZoom) {
  border-radius: 8px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* 地图信息窗口样式优化 */
.map-container :global(.BMap_pop) {
  border-radius: 12px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  border: none !important;
  overflow: hidden !important;
}

.map-container :global(.BMap_pop_top) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  border-radius: 12px 12px 0 0 !important;
  border: none !important;
}

.map-container :global(.BMap_pop_content) {
  border: none !important;
  border-radius: 0 0 12px 12px !important;
  background: white !important;
}

.map-container :global(.BMap_pop_title) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 12px 16px !important;
  border: none !important;
}

/* 地图操作提示 */
.map-section::after {
  content: '🖱️ 拖拽地图浏览周边 | 🔍 滚轮缩放 | 📍 点击标记查看详情';
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  z-index: 10;
  pointer-events: none;
  opacity: 0.8;
}

/* 错误状态 */
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-icon {
  font-size: 64px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-content h3 {
  font-size: 24px;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.error-content p {
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

/* 页脚 */
.footer {
  background: #1f2937;
  color: white;
  padding: 40px 0;
  text-align: center;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 24px;
  color: #dc2626;
}

.footer-logo span {
  font-size: 18px;
  font-weight: 600;
}

.footer p {
  margin: 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 32px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .hero-stats {
    gap: 20px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .activities-grid {
    grid-template-columns: 1fr;
  }
  
  .time-slots {
    grid-template-columns: 1fr;
  }
  
  .images-gallery {
    grid-template-columns: 1fr;
  }
  
  .section {
    padding: 24px;
  }
  
  .container {
    padding: 0 16px;
  }
}
</style> 