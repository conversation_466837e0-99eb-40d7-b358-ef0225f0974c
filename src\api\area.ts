import { request, ApiResponse } from './index'

// 区划数据接口
export interface AreaItem {
  city: string
  cityCode: string
}

// 区划列表响应接口
export interface AreaListResponse {
  code: string
  data: AreaItem[]
  msg: string
}

/**
 * 获取区划分类列表
 * @returns Promise<AreaListResponse>
 */
export const getAreaList = (): Promise<ApiResponse<AreaItem[]>> => {
  return request.get<AreaItem[]>('/province/areaList')
}

// 导出所有区划相关的API
export const areaApi = {
  getAreaList
}
