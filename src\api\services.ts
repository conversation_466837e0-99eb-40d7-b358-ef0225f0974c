import { 
  getCategoryList, 
  getBaseList, 
  getAreaList,
  type CategoryItem,
  type BaseItem,
  type AreaItem,
  type CategoryListParams,
  type BaseListParams
} from './index'

/**
 * API服务类 - 提供业务逻辑封装
 */
export class ApiService {
  /**
   * 获取分类列表
   * @param params 查询参数
   * @returns Promise<CategoryItem[]>
   */
  static async fetchCategories(params?: CategoryListParams): Promise<CategoryItem[]> {
    try {
      const response = await getCategoryList(params)
      return response.data || []
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return []
    }
  }

  /**
   * 获取基地列表
   * @param params 查询参数
   * @returns Promise<BaseItem[]>
   */
  static async fetchBases(params?: BaseListParams): Promise<BaseItem[]> {
    try {
      const response = await getBaseList(params)
      return response.data || []
    } catch (error) {
      console.error('获取基地列表失败:', error)
      return []
    }
  }

  /**
   * 获取区划列表
   * @returns Promise<AreaItem[]>
   */
  static async fetchAreas(): Promise<AreaItem[]> {
    try {
      const response = await getAreaList()
      return response.data || []
    } catch (error) {
      console.error('获取区划列表失败:', error)
      return []
    }
  }

  /**
   * 根据分类ID获取基地列表
   * @param categoryId 分类ID
   * @returns Promise<BaseItem[]>
   */
  static async fetchBasesByCategory(categoryId: number): Promise<BaseItem[]> {
    try {
      const response = await getBaseList({ title: categoryId.toString() })
      return response.data || []
    } catch (error) {
      console.error('根据分类获取基地列表失败:', error)
      return []
    }
  }

  /**
   * 根据区域代码获取基地列表
   * @param areaCode 区域代码
   * @returns Promise<BaseItem[]>
   */
  static async fetchBasesByArea(areaCode: string): Promise<BaseItem[]> {
    try {
      const response = await getBaseList({ areaCode })
      return response.data || []
    } catch (error) {
      console.error('根据区域获取基地列表失败:', error)
      return []
    }
  }

  /**
   * 搜索基地
   * @param keyword 搜索关键词
   * @returns Promise<BaseItem[]>
   */
  static async searchBases(keyword: string): Promise<BaseItem[]> {
    try {
      const response = await getBaseList({ title: keyword })
      return response.data || []
    } catch (error) {
      console.error('搜索基地失败:', error)
      return []
    }
  }
}

// 导出默认实例
export default ApiService
