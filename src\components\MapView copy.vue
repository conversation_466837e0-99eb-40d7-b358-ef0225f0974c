<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, nextTick, watch } from 'vue'
import { Location } from '@element-plus/icons-vue'
import type { PracticeBase } from '../types'



// 处理导航功能的函数 - 直接打开路线规划
const handleNavigation = async (base: PracticeBase) => {
  try {
    console.log('🧭 开始导航到:', base.name);
    
    const el = document.getElementById(`nav-link-${base.id}`);
    if (el) {
      el.classList.add('navigating');
    }
    
    // 目标坐标
    const destPoint = base.coordinates;
    console.log('🎯 目标坐标:', destPoint);
    
    // 检测设备类型和应用支持
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    
    let navUrl = '';
    
    if (isMobile) {
      if (isIOS) {
        // iOS设备 - 尝试打开百度地图APP，失败则使用网页版
        navUrl = `baidumap://map/direction?destination=${destPoint.lat},${destPoint.lng}&mode=driving&src=webapp.baidu.openapi`;
        
        // 创建一个隐藏的iframe来尝试打开APP
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = navUrl;
        document.body.appendChild(iframe);
        
        // 2秒后如果APP没有打开，则使用网页版
        setTimeout(() => {
          document.body.removeChild(iframe);
          // 网页版路线规划URL
          const webUrl = `https://map.baidu.com/mobile/webapp/place/detail/qt=nav&dest_name=${encodeURIComponent(base.name)}&dest=${destPoint.lat},${destPoint.lng}&src=webapp.baidu.openapi`;
          window.open(webUrl, '_blank');
        }, 2000);
        
      } else if (isAndroid) {
        // Android设备 - 尝试Intent或者直接使用网页版路线规划
        try {
          // 构建Intent URL尝试打开百度地图APP
          const intentUrl = `intent://map/direction?destination=${destPoint.lat},${destPoint.lng}&mode=driving&src=webapp.baidu.openapi#Intent;scheme=baidumap;package=com.baidu.BaiduMap;end`;
          window.location.href = intentUrl;
        } catch (intentError) {
          console.log('Intent调用失败，使用网页版');
          // 降级到网页版
          navUrl = `https://map.baidu.com/mobile/webapp/place/detail/qt=nav&dest_name=${encodeURIComponent(base.name)}&dest=${destPoint.lat},${destPoint.lng}&src=webapp.baidu.openapi`;
          window.open(navUrl, '_blank');
        }
      } else {
        // 其他移动设备 - 直接使用网页版路线规划
        navUrl = `https://map.baidu.com/mobile/webapp/place/detail/qt=nav&dest_name=${encodeURIComponent(base.name)}&dest=${destPoint.lat},${destPoint.lng}&src=webapp.baidu.openapi`;
        window.open(navUrl, '_blank');
      }
    } else {
      // 桌面设备 - 直接使用网页版路线规划
      navUrl = `https://map.baidu.com/search/${encodeURIComponent(base.name)}/@${destPoint.lng},${destPoint.lat},19z?querytype=s&da_src=shareurl&wd=${encodeURIComponent(base.name)}&c=78&src=0&pn=0&sug=0&l=19&b=(${destPoint.lng-0.01},${destPoint.lat-0.01};${destPoint.lng+0.01},${destPoint.lat+0.01})&from=webmap&biz_forward=%7B%22scaler%22:2,%22styles%22:%22pl%22%7D&device_ratio=2&tn=B_NORMAL_MAP&nn=0&u_loc=&ie=utf-8&t=${Date.now()}`;
      
      // 打开新窗口显示路线规划页面
      const newWindow = window.open(navUrl, '_blank');
      
      // 如果弹窗被阻止，提示用户
      if (!newWindow) {
        alert('请允许弹窗以打开地图导航页面');
      }
    }
    
    console.log('🔗 导航URL:', navUrl);
    
    // 恢复地址区域状态
    if (el) {
      setTimeout(() => {
        el.classList.remove('navigating');
      }, 1500);
    }
    
  } catch (error) {
    console.error('❌ 导航过程中出错:', error);
    
    // 降级方案：搜索目标地点
    const fallbackUrl = `https://map.baidu.com/search/${encodeURIComponent(base.name)}?querytype=s&da_src=shareurl&wd=${encodeURIComponent(base.name)}&c=78&src=0&pn=0&sug=0&l=12`;
    
    const el = document.getElementById(`nav-link-${base.id}`);
    if (el) {
      el.classList.remove('navigating');
      el.classList.add('nav-error');
      
      // 直接打开降级URL
      window.open(fallbackUrl, '_blank');
      
      // 清除错误状态
      setTimeout(() => {
        el.classList.remove('nav-error');
      }, 2000);
    }
  }
};

interface Props {
  bases: PracticeBase[]
  defaultCenter?: { lat: number, lng: number }
  defaultZoom?: number
  panelVisible?: boolean
  hideMapLegend?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultCenter: () => ({ lat: 26.646412, lng: 114.160148 }),
  defaultZoom: 16,
  panelVisible: false,
  hideMapLegend: false
})

const emit = defineEmits<{
  baseClick: [base: PracticeBase]
  baseLocate: [base: PracticeBase]
}>()

const mapContainer = ref<HTMLElement>()
const isMapReady = ref(false)
const activeLegendItems = ref<Set<string>>(new Set())
const highlightedMarkerId = ref<string | null>(null)
let map: any = null
let markers: any[] = []
let allMarkers: any[] = []

// 定义不同类别的标记图标颜色
const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    '习近平总书记足迹': '#e60000',
    '井冈山精神': '#e6a23c', 
    '红色旅游': '#AA381E',
    '革命遗址': '#f56c6c',
    '英烈纪念': '#909399'
  }
  return colors[category] || '#409eff'
}

// 创建自定义标记图标
const createCustomIcon = (category: string, isHighlighted = false) => {
  const color = getCategoryColor(category)
  const size = isHighlighted ? { width: 48, height: 60 } : { width: 32, height: 40 }
  const iconSize = new (window as any).BMapGL.Size(size.width, size.height)
  const anchor = new (window as any).BMapGL.Size(size.width / 2, size.height)
  
  if (isHighlighted) {
    // 高亮版本：更大、更明显的颜色对比、无动画
    return new (window as any).BMapGL.Icon(
      `data:image/svg+xml;base64,${btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="60" viewBox="0 0 48 60">
          <defs>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
            <radialGradient id="highlightGradient" cx="50%" cy="40%" r="60%">
              <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1"/>
              <stop offset="50%" style="stop-color:${color};stop-opacity:1"/>
              <stop offset="100%" style="stop-color:#FF4500;stop-opacity:1"/>
            </radialGradient>
          </defs>
          
          <!-- 外发光背景圈 -->
          <circle cx="24" cy="24" r="22" fill="#FFD700" opacity="0.3"/>
          
          <!-- 主标记 - 更大尺寸 -->
          <path d="M24 2C14.1 2 6 10.1 6 20c0 18 18 28 18 28s18-10 18-28C42 10.1 33.9 2 24 2z" 
                fill="url(#highlightGradient)" 
                filter="url(#glow)" 
                stroke="#FFD700" 
                stroke-width="4"/>
          
          <!-- 白色内圈 -->
          <circle cx="24" cy="20" r="12" fill="white" stroke="#FFD700" stroke-width="2"/>
          
          <!-- 彩色中心点 -->
          <circle cx="24" cy="20" r="8" fill="${color}"/>
          
          <!-- 高亮中心指示器 -->
          <circle cx="24" cy="20" r="4" fill="#FFD700"/>
          <circle cx="24" cy="20" r="2" fill="white"/>
        </svg>
      `)}`,
      iconSize,
      { anchor }
    )
  } else {
    // 普通版本
    return new (window as any).BMapGL.Icon(
      `data:image/svg+xml;base64,${btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="40" viewBox="0 0 32 40">
          <path d="M16 0C7.2 0 0 7.2 0 16c0 16 16 24 16 24s16-8 16-24C32 7.2 24.8 0 16 0z" fill="${color}"/>
          <circle cx="16" cy="16" r="8" fill="white"/>
          <circle cx="16" cy="16" r="4" fill="${color}"/>
        </svg>
      `)}`,
      iconSize,
      { anchor }
    )
  }
}



const initMap = () => {
  if (!mapContainer.value || !(window as any).BMapGL) {
    console.error('百度地图WebGL API未加载或容器不存在')
    isMapReady.value = false
    return
  }

  try {
    // 验证坐标转换功能
    if (!(window as any).BMapGL.Convertor) {
      console.error('⚠️ 百度地图坐标转换库(Convertor)未加载，请检查API加载URL')
      console.log('💡 当前API URL应包含 &library=CoordConverter 参数')
    } else {
      console.log('✅ 百度地图坐标转换库加载成功')
    }

    // 创建地图实例
    map = new (window as any).BMapGL.Map(mapContainer.value)
    
    // 使用props传入的中心点坐标
    const center = new (window as any).BMapGL.Point(props.defaultCenter.lng, props.defaultCenter.lat)
    
    // 初始化地图，设置中心点和缩放级别
    map.centerAndZoom(center, props.defaultZoom)
    
    // 启用滚轮缩放
    map.enableScrollWheelZoom(true)

      //   // 1. 实时路况
      // const trafficLayer = new (window as any).BMapGL.TrafficLayer()
      // map.addTileLayer(trafficLayer)

      // 2. 街景覆盖
      // const panoLayer = new (window as any).BMapGL.PanoramaCoverageLayer()
      // map.addTileLayer(panoLayer)

      // map.enableHighResolution()     // Retina 屏幕更清晰
      // map.showLabel(true)            // 强制显示地图自带文字标签

    
    // 添加地图控件（WebGL版本支持的控件）
    map.addControl(new (window as any).BMapGL.ScaleControl())
    map.addControl(new (window as any).BMapGL.MapTypeControl())
    map.addControl(new (window as any).BMapGL.ZoomControl())
    
    // 设置地图样式为普通地图
    map.setMapType((window as any).BMAP_NORMAL_MAP)
    
    // 添加地图点击事件（点击空白处取消高亮）
    map.addEventListener('click', (e: any) => {
      // 检查是否点击的是地图背景而不是标记
      if (!e.overlay) {
        // 点击地图空白处取消高亮
        clearHighlight()
      }
    })
    
    isMapReady.value = true
    
    // 添加标记点
    addMarkers()
    
  } catch (error) {
    console.error('地图初始化失败:', error)
    isMapReady.value = false
  }
}

// 创建信息窗口内容的辅助函数
const createInfoWindowContent = (base: PracticeBase) => {
  return `
    <div style="padding: 8px; max-width: 320px; background: #ffffff; border-radius: 8px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
      <!-- 标题区域 -->
      <div style="margin-bottom: 14px;">
        <h4 style="margin: 0 0 4px 0; color: #1f2937; font-size: 16px; font-weight: 600; line-height: 1.3;">${base.name}</h4>
        <span style="display: inline-block; background: ${getCategoryColor(base.category)}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: 500;">
          ${base.category}
        </span>
      </div>
      
      <!-- 地址信息 - 点击直接导航 -->
      <a href="javascript:void(0)" 
         id="nav-link-${base.id}"
         style="display: block; 
                text-decoration: none; 
                margin-bottom: 12px; 
                padding: 12px; 
                background: #f9fafb; 
                border-radius: 6px; 
                border-left: 3px solid ${getCategoryColor(base.category)};
                transition: all 0.25s ease;
                cursor: pointer;
                position: relative;
                border: 1px solid #e5e7eb;"
         onmouseover="this.style.background='#e6f3ff'; this.style.borderColor='#409eff'; this.style.boxShadow='0 2px 8px rgba(64, 158, 255, 0.15)'; this.style.transform='translateY(-1px)';" 
         onmouseout="this.style.background='#f9fafb'; this.style.borderColor='#e5e7eb'; this.style.boxShadow='none'; this.style.transform='translateY(0)';">
        
        <div style="display: flex; align-items: flex-start; gap: 6px;">
          <span style="color: ${getCategoryColor(base.category)}; font-size: 14px; margin-top: 1px;">📍</span>
          <div style="flex: 1;">
            <p style="margin: 0 0 4px 0; color: #374151; font-size: 13px; font-weight: 500; line-height: 1.4; text-decoration: underline; text-decoration-color: ${getCategoryColor(base.category)}; text-underline-offset: 2px;">${base.address}</p>
            ${base.phone ? `<p style="margin: 0; color: #6b7280; font-size: 12px;">📞 ${base.phone}</p>` : ''}
          </div>
          <!-- 导航图标提示 -->
          <span style="color: #409eff; font-size: 16px; opacity: 0.7; transition: all 0.2s ease;">🧭</span>
        </div>
      </a>
      
      <!-- 操作按钮 -->
      <div style="display: flex; gap: 8px; justify-content: flex-end; margin-top: 8px; align-items: center;">
        <a href="javascript:void(0)" onclick="window.viewBaseDetail('${base.id}')" 
           onmouseover="this.style.color='#1e40af'; this.style.textDecoration='underline';" 
           onmouseout="this.style.color='#2563eb'; this.style.textDecoration='none';"
           style="color: #2563eb; text-decoration: none; font-size: 13px; font-weight: 500; cursor: pointer; display: inline-flex; align-items: center; gap: 4px; transition: all 0.2s ease;">
          <span>查看基地详情页</span>
          <span style="font-size: 12px;">→</span>
        </a>
      </div>
    </div>
  `
}

const addMarkers = () => {
  if (!map) return
  
  // 清除现有标记
  clearMarkers()
  
  // 重置计数器
  let markersProcessed = 0
  const totalMarkersToProcess = props.bases.length
  
  if (totalMarkersToProcess === 0) {
    return
  }
  
  props.bases.forEach(base => {
    try {
      // 创建原始坐标点（WGS-84或GCJ-02坐标系）
      const originalPoint = new (window as any).BMapGL.Point(base.coordinates.lng, base.coordinates.lat)
      
      console.log(`开始为 ${base.name} 进行坐标转换`)
      
      // 使用百度地图官方坐标转换API
      if ((window as any).BMapGL && (window as any).BMapGL.Convertor) {
        // const convertor = new (window as any).BMapGL.Convertor()
        // const pointsToConvert = [originalPoint]
        createMarkerWithPoint(base, originalPoint)
        
        // 转换坐标：从WGS-84转换到BD-09
        // convertor.translate(pointsToConvert, 1, 5, (result: any) => {
        //   let convertedPoint = originalPoint
          
        //   if (result.status === 0) {
        //     convertedPoint = result.points[0]
        //     console.log(`✅ ${base.name} WGS-84坐标转换成功`)
        //   } else {
        //     console.warn(`⚠️ ${base.name} WGS-84坐标转换失败，尝试GCJ-02转换...`)
            
        //     // 尝试从GCJ-02转换到BD-09
        //     convertor.translate([originalPoint], 3, 5, (result2: any) => {
        //       if (result2.status === 0) {
        //         convertedPoint = result2.points[0]
        //         console.log(`✅ ${base.name} GCJ-02坐标转换成功`)
        //       } else {
        //         console.warn(`⚠️ ${base.name} 坐标转换失败，使用原始坐标`)
        //         convertedPoint = originalPoint
        //       }
              
        //       createMarkerWithPoint(base, convertedPoint)
        //     })
        //     return // 避免重复创建标记
        //   }
          
        //   createMarkerWithPoint(base, convertedPoint)
        // })
      } else {
        console.warn('⚠️ 百度地图坐标转换功能不可用，使用原始坐标')
      }
      
    } catch (error) {
      console.error(`❌ 添加标记失败 (${base.name}):`, error)
      markersProcessed++
    }
  })
  
    // 创建标记的辅助函数
  function createMarkerWithPoint(base: PracticeBase, point: any) {
    try {
      const marker = new (window as any).BMapGL.Marker(point, {
        icon: createCustomIcon(base.category)
      })
      
      // 创建标记对象，包含基地信息
      const markerObj = {
        marker,
        base,
        category: base.category,
        point
      }
      
      // 添加到所有标记数组
      allMarkers.push(markerObj)
      
      // 检查是否应该显示（根据筛选条件）
      const shouldShow = activeLegendItems.value.size === 0 || activeLegendItems.value.has(base.category)
      
      if (shouldShow) {
        // 添加标记到地图
        map.addOverlay(marker)
        markers.push(markerObj)
      }
      
      // 使用统一的信息窗口内容创建函数
      const infoWindowContent = createInfoWindowContent(base)
      
      const infoWindow = new (window as any).BMapGL.InfoWindow(infoWindowContent, {
        width: 310,
        height: 215,
        enableCloseOnClick: false,
        offset: new (window as any).BMapGL.Size(0, -40) // 向上偏移40像素，避免遮盖标记点
      })
      
      // 标记点击事件
      marker.addEventListener('click', () => {
        map.openInfoWindow(infoWindow, point)
        // 只触发列表定位事件，不触发页面跳转
        emit('baseLocate', base)
        
        // 使用nextTick确保DOM已经渲染
        nextTick(() => {
          // 找到导航按钮并绑定事件
          const navButton = document.getElementById(`nav-link-${base.id}`)
          if (navButton) {
            console.log('🔄 绑定导航按钮事件:', base.name)
            navButton.onclick = (e) => {
              e.preventDefault()
              e.stopPropagation()
              handleNavigation(base)
            }
          } else {
            console.warn('⚠️ 未找到导航按钮:', `nav-link-${base.id}`)
          }
        })
      })
      
      // // 鼠标悬停效果
      // marker.addEventListener('mouseover', () => {
      //   marker.setAnimation((window as any).BMAP_ANIMATION_BOUNCE)
      // })
      
      marker.addEventListener('mouseout', () => {
        marker.setAnimation(null)
      })
      
      markersProcessed++
      
    } catch (error) {
      console.error(`❌ 创建标记失败 (${base.name}):`, error)
      markersProcessed++
    }
  }
  }

const clearMarkers = () => {
  markers.forEach(markerObj => {
    map.removeOverlay(markerObj.marker)
  })
  markers = []
  allMarkers = []
}





// 图例点击处理函数
const handleLegendClick = (category: string) => {
  console.log('图例点击:', category)
  if (activeLegendItems.value.has(category)) {
    // 如果已选中，则取消选中
    activeLegendItems.value.delete(category)
    console.log('取消选中:', category)
  } else {
    // 如果未选中，则选中
    activeLegendItems.value.add(category)
    console.log('选中:', category)
  }
  
  // 触发筛选
  filterMarkers()
  
  // 自动调整视图以适应筛选后的基地
  setTimeout(() => {
    updateMapView()
  }, 300)
}

// 筛选标记点
const filterMarkers = () => {
  if (!map) return
  
  allMarkers.forEach(markerObj => {
    const shouldShow = activeLegendItems.value.size === 0 || activeLegendItems.value.has(markerObj.category)
    
    if (shouldShow) {
      // 显示标记
      if (!map.getOverlays().includes(markerObj.marker)) {
        map.addOverlay(markerObj.marker)
      }
    } else {
      // 隐藏标记
      map.removeOverlay(markerObj.marker)
      
      // 如果隐藏的标记是当前高亮的，清除高亮状态
      if (highlightedMarkerId.value === markerObj.base.id) {
        clearHighlight()
      }
    }
  })
  
  // 更新显示的标记数组
  markers = allMarkers.filter(markerObj => 
    activeLegendItems.value.size === 0 || activeLegendItems.value.has(markerObj.category)
  )
}

// 获取当前显示的基地列表
const getVisibleBases = () => {
  if (activeLegendItems.value.size === 0) {
    return props.bases
  }
  return props.bases.filter(base => activeLegendItems.value.has(base.category))
}

// 重置筛选
const resetFilter = () => {
  console.log('重置图例筛选')
  activeLegendItems.value.clear()
  filterMarkers()
  
  // 自动调整视图以适应所有基地
  setTimeout(() => {
    updateMapView()
  }, 300)
}

// 统一的地图视图更新函数，使用百度地图官方的setViewport方法
const updateMapView = () => {
  if (!map || !isMapReady.value) {
    console.log('地图未准备好，跳过视图更新')
    return
  }
  
  try {
    // 获取当前可见的基地
    const visibleBases = getVisibleBases()
    if (visibleBases.length === 0) {
      console.log('没有可见的基地，返回默认视图')
      // 如果没有可见基地，返回默认视图
      const defaultCenter = new (window as any).BMapGL.Point(props.defaultCenter.lng, props.defaultCenter.lat)
      map.centerAndZoom(defaultCenter, props.defaultZoom)
      return
    }
    
    console.log('更新地图视图，可见基地数量:', visibleBases.length)
    
    // 创建所有基地的点数组
    const points = visibleBases.map(base => 
      new (window as any).BMapGL.Point(base.coordinates.lng, base.coordinates.lat)
    )
    
    // 计算面板占用的视图边距
    const margins = props.panelVisible 
      ? [10, 420, 10, 10]  // 上、右、下、左边距（右侧面板宽度420px）
      : [10, 10, 10, 10]   // 均匀边距
    
    // 使用setViewport一步到位地调整视图，可以自动处理边距、缩放和中心点
    map.setViewport(points, {
      margins: margins,
      zoomFactor: -0.5,  // 稍微缩小一点，看到更多内容
      enableAnimation: true,  // 启用动画效果
      // maxZoom: props.defaultZoom  // 限制最大缩放级别为defaultZoom
    })
    
  } catch (error) {
    console.warn('更新地图视图失败:', error)
    // 降级方案：使用默认中心点
    const defaultCenter = new (window as any).BMapGL.Point(props.defaultCenter.lng, props.defaultCenter.lat)
    map.centerAndZoom(defaultCenter, props.defaultZoom)
  }
}

// 高亮管理函数
const highlightMarker = (baseId: string) => {
  console.log('🎯 开始高亮标记:', baseId)
  console.log('📊 当前所有标记数量:', allMarkers.length)
  
  // 取消之前的高亮
  clearHighlight()
  
  // 设置新的高亮
  highlightedMarkerId.value = baseId
  console.log('🔥 设置高亮ID:', highlightedMarkerId.value)
  
  // 更新对应标记的图标
  const markerObj = allMarkers.find(m => m.base.id === baseId)
  console.log('🔍 查找标记结果:', markerObj ? `找到: ${markerObj.base.name}` : '未找到')
  
  if (markerObj) {
    try {
      console.log('🎨 开始创建高亮图标，类别:', markerObj.base.category)
      const highlightedIcon = createCustomIcon(markerObj.base.category, true)
      console.log('✨ 高亮图标创建成功')
      
      console.log('🖼️ 开始更新标记图标...')
      markerObj.marker.setIcon(highlightedIcon)
      console.log('✅ 标记图标更新完成')
      
      // 设置更高的层级，确保高亮标记在最上层
      markerObj.marker.setZIndex(999)
      console.log('📍 设置标记层级为999')
      
      // 强制刷新标记显示
      if (map && map.removeOverlay && map.addOverlay) {
        console.log('🔄 强制刷新标记显示...')
        map.removeOverlay(markerObj.marker)
        map.addOverlay(markerObj.marker)
        console.log('✨ 标记显示刷新完成')
      }
      
      console.log(`🎉 成功高亮标记: ${markerObj.base.name}`)
    } catch (error) {
      console.error('❌ 高亮标记过程中发生错误:', error)
    }
  } else {
    console.warn(`⚠️ 未找到要高亮的标记: ${baseId}`)
    console.log('🔍 所有可用标记:', allMarkers.map(m => ({ id: m.base.id, name: m.base.name })))
  }
}

const clearHighlight = () => {
  if (highlightedMarkerId.value) {
    console.log('🧹 开始清除高亮:', highlightedMarkerId.value)
    
    const markerObj = allMarkers.find(m => m.base.id === highlightedMarkerId.value)
    if (markerObj) {
      try {
        console.log('🔄 恢复普通图标...')
        // 恢复普通图标
        const normalIcon = createCustomIcon(markerObj.base.category, false)
        markerObj.marker.setIcon(normalIcon)
        markerObj.marker.setZIndex(1)
        
        // 强制刷新标记显示
        if (map && map.removeOverlay && map.addOverlay) {
          map.removeOverlay(markerObj.marker)
          map.addOverlay(markerObj.marker)
        }
        
        console.log(`✅ 成功清除高亮: ${markerObj.base.name}`)
      } catch (error) {
        console.error('❌ 清除高亮过程中发生错误:', error)
      }
    }
  }
  highlightedMarkerId.value = null
  console.log('🏁 高亮状态已清除')
}

const updateMarkerHighlight = (baseId: string, isHighlighted: boolean) => {
  const markerObj = allMarkers.find(m => m.base.id === baseId)
  if (markerObj) {
    const icon = createCustomIcon(markerObj.base.category, isHighlighted)
    markerObj.marker.setIcon(icon)
    markerObj.marker.setZIndex(isHighlighted ? 999 : 1)
    
    if (isHighlighted) {
      highlightedMarkerId.value = baseId
    } else if (highlightedMarkerId.value === baseId) {
      highlightedMarkerId.value = null
    }
    
    console.log(`✅ 更新标记高亮状态: ${markerObj.base.name}, 高亮: ${isHighlighted}`)
  }
}

// 全局函数，供信息窗口中的按钮调用
;(window as any).viewBaseDetail = (baseId: string) => {
  console.log('点击查看详情按钮，基地ID:', baseId)
  const base = props.bases.find(b => b.id === baseId)
  if (base) {
    // 对于黄洋界哨口基地，跳转到新的思政课基地详情页面
    if (base.name === '黄洋界哨口') {
      // 使用window.location进行跳转，确保在任何环境下都能正常工作
      window.location.href = `/political-base/${base.id}`
    } else if (base.websiteUrl) {
    console.log('打开基地网站:', base.name, base.websiteUrl)
    // 直接打开基地官网
    window.open(base.websiteUrl, '_blank')
  } else {
    console.error('未找到基地信息或网站地址，ID:', baseId)
    }
  } else {
    console.error('未找到基地信息，ID:', baseId)
  }
}

// 全局函数，供信息窗口中的定位按钮调用
;(window as any).locateInList = (baseId: string) => {
  const base = props.bases.find(b => b.id === baseId)
  if (base) {
    emit('baseLocate', base)
  }
}

// 定位到指定基地并打开信息窗口
const locateAndOpenInfoWindow = (base: PracticeBase) => {
  console.log('🎯 开始定位并打开信息窗口:', base.name, '基地ID:', base.id)
  
  if (!map || !base) {
    console.error('❌ 地图或基地信息无效:', { map: !!map, base: !!base })
    return
  }
  
  try {
    console.log('🔥 调用高亮标记功能...')
    // 先高亮标记
    highlightMarker(base.id)
    
    // 找到对应的标记点
    const markerObj = allMarkers.find(m => m.base.id === base.id)
    if (!markerObj) {
      console.warn('⚠️ 未找到对应的标记点:', base.name)
      console.log('🔍 当前所有标记:', allMarkers.map(m => ({ id: m.base.id, name: m.base.name })))
      return
    }
    
    console.log('✅ 找到标记点:', markerObj.base.name)
    
    // 获取当前缩放级别
    const currentZoom = map.getZoom()
    console.log('📐 当前缩放级别:', currentZoom)
    
    // 设置目标缩放级别：如果当前级别小于15，则放大到15级，否则保持当前级别
    const targetZoom = Math.max(currentZoom, 15)
    console.log('🎯 目标缩放级别:', targetZoom)
    
    // 先设置缩放级别，再平滑移动到标记点位置
    if (targetZoom > currentZoom) {
      console.log('🔍 需要放大地图，先放大再移动')
      // 如果需要放大，先放大再移动
      map.setZoom(targetZoom)
      setTimeout(() => {
        console.log('📍 开始移动到标记点位置')
        map.panTo(markerObj.point)
      }, 200)
    } else {
      console.log('📍 直接移动到标记点位置')
      // 如果不需要放大，直接移动
      map.panTo(markerObj.point)
    }
    
    const delayTime = targetZoom > currentZoom ? 700 : 500
    console.log(`⏰ 延迟 ${delayTime}ms 后打开信息窗口`)
    
    // 延迟打开信息窗口，确保地图移动和缩放完成
    setTimeout(() => {
      console.log('💬 开始创建信息窗口...')
      // 创建并打开信息窗口
      const infoWindowContent = createInfoWindowContent(base)
      const infoWindow = new (window as any).BMapGL.InfoWindow(infoWindowContent, {
        width: 320,
        height: 240,
        enableCloseOnClick: false,
        offset: new (window as any).BMapGL.Size(0, -60) // 增加偏移，适应高亮标记的更大尺寸
      })
      
      // 监听信息窗口关闭事件
      infoWindow.addEventListener('close', () => {
        console.log('💬 信息窗口关闭，清除高亮')
        clearHighlight()
      })
      
      // 打开信息窗口
      console.log('💬 打开信息窗口')
      map.openInfoWindow(infoWindow, markerObj.point)
      
      // 添加标记点弹跳动画效果
      console.log('🎪 添加弹跳动画效果')
      markerObj.marker.setAnimation((window as any).BMAP_ANIMATION_BOUNCE)
      setTimeout(() => {
        markerObj.marker.setAnimation(null)
        console.log('🎪 弹跳动画结束')
      }, 2000)
      
      // 使用nextTick绑定导航按钮事件
      nextTick(() => {
        const navButton = document.getElementById(`nav-link-${base.id}`)
        if (navButton) {
          console.log('🧭 绑定导航按钮事件:', base.name)
          navButton.onclick = (e) => {
            e.preventDefault()
            e.stopPropagation()
            handleNavigation(base)
          }
        }
      })
      
      console.log(`🎉 信息窗口已打开并高亮标记: ${base.name}`)
      
    }, delayTime)
    
  } catch (error) {
    console.error('❌ 定位基地失败:', error)
  }
}

// 监听bases变化，重新添加标记
watch(() => props.bases, () => {
  if (map) {
    addMarkers()
  }
}, { deep: true })

 

onMounted(() => {
  // 检查百度地图WebGL API是否已加载
  const checkBMapAPI = () => {
    if ((window as any).BMapGL) {
      nextTick(() => {
        initMap()
      })
    } else {
      // 如果API还未加载，等待一段时间后重试
      setTimeout(checkBMapAPI, 100)
    }
  }
  
  checkBMapAPI()
  })
 
// 暴露方法给父组件调用
defineExpose({
  locateAndOpenInfoWindow,
  updateMapView,
  highlightMarker,
  clearHighlight,
  updateMarkerHighlight
})
</script>

<template>
  <div class="map-container">
    <!-- Loading overlay -->
    <div v-if="!isMapReady" class="map-loading">
      <div class="loading-content">
        <el-icon class="loading-icon" size="64"><Location /></el-icon>
        <h3>正在加载地图...</h3>
        <p>请稍候，正在为您加载井冈山红色教育实践基地地图</p>
      </div>
    </div>
    
    <!-- Map container - always present in DOM -->
    <div class="map-wrapper">
      <div ref="mapContainer" class="baidu-map"></div>
      
      <!-- Map Legend - 移动到右下角 -->
      <div v-show="isMapReady && !hideMapLegend" class="map-legend">
        <div class="legend-header">
          <h4>图例</h4>
          <el-button 
            v-if="activeLegendItems.size > 0" 
            @click="resetFilter" 
            type="primary" 
            text 
            size="small"
            class="reset-filter-btn">
            全部
          </el-button>
        </div>
        <div 
          class="legend-item" 
          :class="{ active: activeLegendItems.has('习近平总书记足迹') }"
          @click="handleLegendClick('习近平总书记足迹')">
          <div class="legend-color" style="background: #e60000"></div>
          <span>习近平总书记足迹</span>
        </div>
        <div 
          class="legend-item" 
          :class="{ active: activeLegendItems.has('井冈山精神') }"
          @click="handleLegendClick('井冈山精神')">
          <div class="legend-color" style="background: #e6a23c"></div>
          <span>井冈山精神</span>
        </div>

        <div 
          class="legend-item" 
          :class="{ active: activeLegendItems.has('革命遗址') }"
          @click="handleLegendClick('革命遗址')">
          <div class="legend-color" style="background: #f56c6c"></div>
          <span>革命遗址</span>
        </div>
        <div 
          class="legend-item" 
          :class="{ active: activeLegendItems.has('英烈纪念') }"
          @click="handleLegendClick('英烈纪念')">
          <div class="legend-color" style="background: #909399"></div>
          <span>英烈纪念</span>
        </div>
      </div>
      

      
              <!-- Base Count - 右下角 -->
        <div v-show="isMapReady && !hideMapLegend" class="base-count">
          <span>共 {{ getVisibleBases().length }} 个基地</span>
          <span v-if="activeLegendItems.size > 0" class="filter-hint">
            （已筛选）
          </span>
        </div>
    </div>
  </div>
</template>

<style scoped>
/* 导航区域样式 */
:deep(.navigating) {
  background: #FFF3E0 !important;
  border-left: 3px solid #FF9800 !important;
  animation: pulse 1s infinite alternate;
  cursor: wait;
}

:deep(.navigating)::after {
  content: '正在获取位置...';
  position: absolute;
  bottom: 8px;
  right: 8px;
  font-size: 12px;
  color: #FF9800;
  padding: 2px 8px;
  background: rgba(255, 152, 0, 0.1);
  border-radius: 4px;
}

:deep(.nav-error) {
  background: #FFF5F5 !important;
  border-left: 3px solid #F44336 !important;
}

:deep(.nav-error)::after {
  content: '位置获取失败，点击重试';
  position: absolute;
  bottom: 8px;
  right: 8px;
  font-size: 12px;
  color: #F44336;
  padding: 2px 8px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
}

.map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  z-index: 2000;
}

.loading-content {
  text-align: center;
  color: #909399;
}


.loading-icon {
  margin-bottom: 16px;
  color: #409eff;
}

.loading-content h3 {
  margin-bottom: 8px;
  color: #303133;
}

.map-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.baidu-map {
  width: 100%;
  height: 100%;
}

/* 图例移动到左下角 */
.map-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 180px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.legend-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

.reset-filter-btn {
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  min-height: auto;
}

.legend-hint {
  font-size: 12px;
  color: #909399;
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-item:hover {
  background: #f5f7fa;
  color: #303133;
  transform: translateX(2px);
}

.legend-item.active {
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
  color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  border: 1px solid #b3d8ff;
}

.legend-item.active::after {
  content: '✓';
  position: absolute;
  right: 8px;
  color: #409eff;
  font-weight: bold;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.legend-item.active .legend-color {
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}



/* 基地数量显示在右下角，图例下方 */
.base-count {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: white;
  padding: 10px 14px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #606266;
  z-index: 1000;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-hint {
  color: #409eff;
  font-size: 12px;
  font-weight: 400;
}

@media (max-width: 768px) {
  .map-legend {
    bottom: 10px;
    left: 10px;
    right: auto;
    min-width: auto;
    max-width: 200px;
    padding: 12px;
  }
  
  .legend-header {
    margin-bottom: 10px;
  }
  
  .legend-item {
    padding: 8px 6px;
    margin-bottom: 6px;
  }
  
  .legend-item.active::after {
    right: 6px;
  }
  
  .base-count {
    bottom: 10px;
    right: 10px;
    left: auto;
    width: auto;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .filter-hint {
    font-size: 11px;
  }
}
</style>