import { request, ApiResponse } from './index'

// 分类列表请求参数接口
export interface CategoryListParams {
  areaCode?: string
  areaName?: string
  title?: string
}

// 分类数据接口
export interface CategoryItem {
  color: string
  id: number
  title: string
}

// 分类列表响应接口
export interface CategoryListResponse {
  code: string
  data: CategoryItem[]
  msg: string
}

/**
 * 获取分类列表
 * @param params 请求参数
 * @returns Promise<CategoryListResponse>
 */
export const getCategoryList = (params?: CategoryListParams): Promise<ApiResponse<CategoryItem[]>> => {
  return request.post<CategoryItem[]>('/category/public/categoryList', params)
}

// 导出所有分类相关的API
export const categoryApi = {
  getCategoryList
}
