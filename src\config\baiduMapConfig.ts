import type { BaiduMapApiConfig } from '../utils/baiduMapApi'
import { env } from './env'

// 百度地图API配置
export const baiduMapConfig: BaiduMapApiConfig = {
  // 使用统一的环境变量管理
  ak: env.BAIDU_MAP_AK,
  
  // 静态地图图片配置
  width: 400,  // 图片宽度
  height: 300, // 图片高度
  zoom: 16     // 缩放级别，16级可以显示较详细的地理信息
}

// 验证配置是否完整
export function validateBaiduMapConfig(): boolean {
  if (!baiduMapConfig.ak) {
    console.warn('⚠️  百度地图API密钥未配置，将使用默认图片')
    console.warn('请设置环境变量 VITE_BAIDU_MAP_AK')
    return false
  }
  
  console.log('✅ 百度地图API配置已加载')
  return true
}

// 环境变量配置说明
export const ENV_SETUP_GUIDE = `
配置步骤：
1. 在项目根目录创建 .env 文件
2. 添加以下内容：
   VITE_BAIDU_MAP_AK=your_baidu_map_api_key_here
   VITE_TIANDITU_MAP_TK=your_tianditu_map_tk_here
3. 重启开发服务器

获取API密钥：
- 百度地图: https://lbsyun.baidu.com/apiconsole/key
- 天地图: https://console.tianditu.gov.cn/api/key
` 