# API模块使用说明

## 概述

本项目使用Axios进行API请求封装，采用模块化开发方式，提供了完整的类型支持和错误处理。

## 目录结构

```
src/api/
├── index.ts          # API主入口文件，配置axios实例
├── category.ts       # 分类相关API
├── base.ts          # 基地相关API
├── area.ts          # 区划相关API
├── services.ts      # API服务层，提供业务逻辑封装
└── README.md        # 使用说明文档
```

## 配置说明

### 环境变量配置

在项目根目录创建 `.env` 文件：

```env
VITE_API_BASE_URL=http://your-api-server.com
```

### API配置

在 `src/config/api.ts` 中可以配置API相关参数：

```typescript
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  TIMEOUT: 10000,
  // ... 其他配置
}
```

## API接口说明

### 1. 分类列表接口

**接口地址**: `POST /category/public/categoryList`

**请求参数**:
```typescript
interface CategoryListParams {
  areaCode?: string  // 区域代码
  areaName?: string  // 区域名称
  title?: string     // 标题
}
```

**响应数据**:
```typescript
interface CategoryItem {
  color: string  // 颜色
  id: number     // 分类ID
  title: string  // 分类标题
}
```

**使用示例**:
```typescript
import { getCategoryList } from '@/api'

// 获取所有分类
const categories = await getCategoryList()

// 根据区域获取分类
const areaCategories = await getCategoryList({ areaCode: '360000' })
```

### 2. 基地列表接口

**接口地址**: `POST /base/public/baseInfoList`

**请求参数**:
```typescript
interface BaseListParams {
  areaCode?: string  // 区域代码
  areaName?: string  // 区域名称
  title?: string     // 标题
}
```

**响应数据**:
```typescript
interface BaseItem {
  address: string      // 地址
  baseDesc: string     // 基地描述
  baseName: string     // 基地名称
  categoryId: number   // 分类ID
  city: string         // 城市
  cityCode: string     // 城市代码
  district: string     // 区县
  districtCode: string // 区县代码
  id: number          // 基地ID
  image: string       // 图片URL
  lat: number         // 纬度
  lng: number         // 经度
  openTime: string    // 开放时间
  phone: string       // 联系电话
  province: string    // 省份
  provinceCode: string // 省份代码
  score: number       // 评分
  title: string       // 标题
}
```

**使用示例**:
```typescript
import { getBaseList } from '@/api'

// 获取所有基地
const bases = await getBaseList()

// 根据区域获取基地
const areaBases = await getBaseList({ areaCode: '360000' })

// 搜索基地
const searchResults = await getBaseList({ title: '井冈山' })
```

### 3. 区划分类接口

**接口地址**: `GET /province/areaList`

**响应数据**:
```typescript
interface AreaItem {
  city: string     // 城市名称
  cityCode: string // 城市代码
}
```

**使用示例**:
```typescript
import { getAreaList } from '@/api'

// 获取区划列表
const areas = await getAreaList()
```

## 服务层使用

### ApiService类

`ApiService` 类提供了更高级的业务逻辑封装：

```typescript
import ApiService from '@/api/services'

// 获取分类列表
const categories = await ApiService.fetchCategories()

// 获取基地列表
const bases = await ApiService.fetchBases()

// 获取区划列表
const areas = await ApiService.fetchAreas()

// 根据分类ID获取基地
const categoryBases = await ApiService.fetchBasesByCategory(1)

// 根据区域代码获取基地
const areaBases = await ApiService.fetchBasesByArea('360000')

// 搜索基地
const searchResults = await ApiService.searchBases('井冈山')
```

## 在Vue组件中使用

### 基本使用

```vue
<template>
  <div>
    <el-button @click="loadData" :loading="loading">
      加载数据
    </el-button>
    <div v-if="bases.length > 0">
      <div v-for="base in bases" :key="base.id">
        {{ base.baseName }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import ApiService from '@/api/services'
import type { BaseItem } from '@/api'

const bases = ref<BaseItem[]>([])
const loading = ref(false)

const loadData = async () => {
  loading.value = true
  try {
    bases.value = await ApiService.fetchBases()
    ElMessage.success('数据加载成功')
  } catch (error) {
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}
</script>
```

### 使用Composables

可以创建自定义的composables来管理API状态：

```typescript
// composables/useBases.ts
import { ref, computed } from 'vue'
import ApiService from '@/api/services'
import type { BaseItem } from '@/api'

export function useBases() {
  const bases = ref<BaseItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const loadBases = async (params?: any) => {
    loading.value = true
    error.value = null
    try {
      bases.value = await ApiService.fetchBases(params)
    } catch (err) {
      error.value = '加载失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const searchBases = async (keyword: string) => {
    loading.value = true
    error.value = null
    try {
      bases.value = await ApiService.searchBases(keyword)
    } catch (err) {
      error.value = '搜索失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    bases: computed(() => bases.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    loadBases,
    searchBases
  }
}
```

## 错误处理

API模块内置了完整的错误处理机制：

1. **网络错误**: 自动显示网络连接失败提示
2. **HTTP状态码错误**: 根据状态码显示相应错误信息
3. **业务错误**: 显示服务器返回的错误信息
4. **请求超时**: 自动处理超时情况

## 类型支持

所有API都提供了完整的TypeScript类型支持：

```typescript
import type { 
  CategoryItem, 
  BaseItem, 
  AreaItem,
  CategoryListParams,
  BaseListParams 
} from '@/api'
```

## 最佳实践

1. **使用服务层**: 优先使用 `ApiService` 而不是直接调用API函数
2. **错误处理**: 在组件中捕获并处理API错误
3. **加载状态**: 为异步操作添加加载状态
4. **类型安全**: 充分利用TypeScript类型支持
5. **环境配置**: 使用环境变量配置API地址

## 示例组件

参考 `src/components/ApiExample.vue` 查看完整的使用示例。
