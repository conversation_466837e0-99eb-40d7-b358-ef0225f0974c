/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'header-blue': '#003399',
        'header-blue-light': '#0055cc',
        'primary-blue': '#409eff',
      },
      animation: {
        'bounce-gentle': 'bounceGentle 2s infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        bounceGentle: {
          '0%, 100%': {
            transform: 'translateY(-5%)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
      },
      boxShadow: {
        'header': '0 2px 8px rgba(0, 0, 0, 0.3)',
        'button': '0 2px 12px rgba(0, 0, 0, 0.15)',
        'button-hover': '0 4px 16px rgba(0, 51, 153, 0.3)',
      },
      backdropBlur: {
        '10': '10px',
      },
    },
  },
  plugins: [],
} 