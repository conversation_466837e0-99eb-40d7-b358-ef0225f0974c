import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// API响应数据接口
export interface ApiResponse<T = any> {
  code: string | number
  data: T
  msg: string
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // 使用代理路径
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么
    console.log('🚀 API请求:', config.method?.toUpperCase(), config.url)
    
    // 可以在这里添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers = {
    //     ...config.headers,
    //     Authorization: `Bearer ${token}`
    //   }
    // }
    
    return config
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 对响应数据做点什么
    console.log('✅ API响应:', response.config.url, response.data)
    
    const { data } = response
    
    // 根据业务状态码处理
    if (data.code === '200' || data.code === 200) {
      return response
    } else {
      // 业务错误处理
      ElMessage.error(data.msg || '请求失败')
      return Promise.reject(new Error(data.msg || '请求失败'))
    }
  },
  (error) => {
    // 对响应错误做点什么
    console.error('❌ 响应拦截器错误:', error)

    let errorMessage = '网络错误'

    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      switch (status) {
        case 400:
          errorMessage = '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          break
        case 403:
          errorMessage = '拒绝访问'
          break
        case 404:
          errorMessage = '请求地址不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data?.msg || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      if (error.code === 'ERR_NETWORK') {
        errorMessage = 'CORS跨域错误或网络连接失败，请检查代理配置'
      } else {
        errorMessage = '网络连接失败，请检查网络'
      }
    } else {
      // 请求配置出错
      errorMessage = error.message || '请求配置错误'
    }

    ElMessage.error(errorMessage)
    return Promise.reject(error)
  }
)

// 通用请求方法
export const request = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return api.get(url, { params })
  },
  
  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return api.post(url, data)
  },
  
  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return api.put(url, data)
  },
  
  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return api.delete(url, { params })
  }
}

// 统一导出所有API模块
export * from './category'
export * from './base'
export * from './area'

export default api
