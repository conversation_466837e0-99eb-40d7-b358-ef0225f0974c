<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { Location, Star } from '@element-plus/icons-vue'
import type { PracticeBase } from '../types'

interface Props {
  base: PracticeBase
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [base: PracticeBase]
}>()

const handleClick = () => {
  emit('click', props.base)
}
</script>

<template>
  <el-card 
    class="base-card"
    :body-style="{ padding: '0' }"
    shadow="hover"
    @click="handleClick"
  >
    <div class="card-image">
      <img :src="base.imageUrl" :alt="base.name" />
      <div class="card-overlay">
        <el-tag type="primary" size="small">{{ base.category }}</el-tag>
      </div>
    </div>
    
    <div class="card-content">
      <h3 class="card-title">{{ base.name }}</h3>
      
      <div class="card-info">
        <div class="info-item">
          <el-icon><Location /></el-icon>
          <span>{{ base.city }}</span>
        </div>
        <div class="info-item">
          <el-icon><Star /></el-icon>
          <span>{{ base.rating }}/5.0</span>
        </div>

      </div>
      
      <p class="card-description">{{ base.description }}</p>
      
      <div class="card-footer">
        <div class="address">
          <el-icon><Location /></el-icon>
          <span>{{ base.address }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<style scoped>
.base-card {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-radius: 12px;
  overflow: hidden;
}

.base-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.base-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 12px;
  left: 12px;
}

.card-content {
  padding: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-info {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 14px;
}

.info-item .el-icon {
  font-size: 16px;
}

.card-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-footer {
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
}

.address {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
}

.address .el-icon {
  font-size: 14px;
}

.address span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>