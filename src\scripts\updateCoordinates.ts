// 批量更新坐标的脚本
import { mockBases } from '../data/mockData'
import { addressToCoordinate, coordinateToAddress } from '../utils/tiandituMapApi'

/**
 * 表示具有坐标的实践基地
 */
interface BaseWithCoordinates {
  id: string
  name: string
  address: string
  coordinates: { lat: number; lng: number } | null
}

/**
 * 验证坐标是否在井冈山地区范围内
 * 井冈山大致范围：
 * 纬度：26.45 - 26.65
 * 经度：114.05 - 114.25
 */
function isInJinggangshanArea(lat: number, lng: number): boolean {
  return (
    lat >= 26.45 && lat <= 26.65 && 
    lng >= 114.05 && lng <= 114.25
  )
}

/**
 * 从基地地址获取坐标，并验证是否在井冈山范围内
 */
async function getCoordinateFromAddress(address: string): Promise<{ lat: number; lng: number } | null> {
  try {
    const result = await addressToCoordinate(address)
    if (result && result.lat && result.lng) {
      const { lat, lng } = result
      
      // 检查坐标是否在井冈山地区范围内
      if (isInJinggangshanArea(lat, lng)) {
        return { lat, lng }
      } else {
        console.warn(`⚠️ 坐标 [${lat}, ${lng}] 不在井冈山地区范围内`)
        return null
      }
    }
    return null
  } catch (error) {
    console.error(`❌ 获取 "${address}" 的坐标失败:`, error)
    return null
  }
}

/**
 * 使用天地图API测试单个地址
 * 返回地址对应的坐标和反向地理编码结果
 */
export async function testSingleAddress(address: string): Promise<{
  coordinates: { lat: number; lng: number } | null
  reverseGeocode: string | null
}> {
  console.log(`🔍 测试地址: ${address}`)
  
  try {
    // 1. 地址 -> 坐标
    const coordinates = await getCoordinateFromAddress(address)
    console.log(`📍 地址坐标:`, coordinates)
    
    // 2. 坐标 -> 地址 (如果坐标有效)
    let reverseGeocode = null
    if (coordinates) {
      const { lat, lng } = coordinates
      try {
        // 将lat/lng作为对象传递给coordinateToAddress
        const reverseResult = await coordinateToAddress({lat, lng})
        reverseGeocode = reverseResult
        console.log(`🏠 反向地理编码:`, reverseGeocode)
      } catch (error) {
        console.error(`❌ 反向地理编码失败:`, error)
      }
    }
    
    return { coordinates, reverseGeocode }
  } catch (error) {
    console.error(`❌ 地址测试失败:`, error)
    return { coordinates: null, reverseGeocode: null }
  }
}

/**
 * 更新所有实践基地的坐标
 * 返回更新后的基地列表
 */
export async function updateAllCoordinates(): Promise<BaseWithCoordinates[]> {
  const updatedBases: BaseWithCoordinates[] = []
  
  console.log(`🔄 开始更新所有实践基地坐标 (共 ${mockBases.length} 个)...`)
  
  for (const base of mockBases) {
    const { id, name, address } = base
    console.log(`🔍 处理 [${id}] ${name}: ${address}`)
    
    const coordinates = await getCoordinateFromAddress(address)
    
    updatedBases.push({
      id,
      name,
      address,
      coordinates
    })
    
    if (coordinates) {
      console.log(`✅ 已更新坐标 [${id}] ${name}: [${coordinates.lat}, ${coordinates.lng}]`)
    } else {
      console.warn(`⚠️ 无法获取坐标 [${id}] ${name}`)
    }
  }
  
  console.log(`🎉 坐标更新完成！共处理了 ${updatedBases.length} 个基地`)
  
  // 返回所有基地的更新结果
  return updatedBases
}

// 在window上暴露坐标更新工具
// 这样在浏览器控制台中可以访问这些函数
if (typeof window !== 'undefined') {
  (window as any).coordinateUpdater = {
    updateAll: updateAllCoordinates,
    testSingle: testSingleAddress
  }
} 