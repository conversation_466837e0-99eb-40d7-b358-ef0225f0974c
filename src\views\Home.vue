<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Location, List, ArrowRight } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'

import MapView from '../components/MapView.vue'
import { mockBases } from '../data/mockData'
import type { PracticeBase } from '../types'

const router = useRouter()
const loading = ref(false)
const rightPanelVisible = ref(true)
const searchQuery = ref('')
const selectedCity = ref('')
const practiceBaseData = ref<PracticeBase[]>([])
const mapViewRef = ref()

// 移动端状态管理
const isMobile = ref(false)

// 检测是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  // 移动端默认隐藏右侧面板
  if (isMobile.value) {
    rightPanelVisible.value = false
  } else {
    rightPanelVisible.value = true
  }
}

const cities = [
  { value: '', label: '全部' },
  { value: '茨坪镇', label: '茨坪镇' },
  { value: '黄洋界', label: '黄洋界' },
  { value: '龙潭', label: '龙潭' },
  { value: '大井', label: '大井' },
  { value: '小井', label: '小井' },
  { value: '三湾', label: '三湾' },
  { value: '荆竹山', label: '荆竹山' },
  { value: '茅坪', label: '茅坪' }
]



const filteredBases = computed(() => {
  let filtered = practiceBaseData.value

  if (searchQuery.value) {
    filtered = filtered.filter(base => 
      base.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      base.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedCity.value) {
    filtered = filtered.filter(base => base.city === selectedCity.value)
  }

  return filtered
})

// 定位到地图标记点并打开信息窗口
const handleMapLocateAndOpenInfoWindow = (base: PracticeBase) => {
  console.log('🏠 Home.vue - 定位并打开信息窗口，基地:', base.name, '基地ID:', base.id)
  
  // 确保右侧面板可见，以便用户可以看到定位效果
  if (!rightPanelVisible.value) {
    console.log('📱 显示右侧面板')
    rightPanelVisible.value = true
  }
  
  // 调用地图组件的定位方法
  if (mapViewRef.value && mapViewRef.value.locateAndOpenInfoWindow) {
    console.log('🗺️ 调用地图组件的定位方法')
    mapViewRef.value.locateAndOpenInfoWindow(base)
  } else {
    console.error('❌ 地图组件或定位方法不可用:', {
      mapViewRef: !!mapViewRef.value,
      locateAndOpenInfoWindow: mapViewRef.value ? !!mapViewRef.value.locateAndOpenInfoWindow : false
    })
  }
}

const handleBaseClick = (base: PracticeBase) => {
  console.log('🏠 Home.vue - handleBaseClick 被调用，基地信息:', base.name, '基地ID:', base.id)
  
  // 定位到地图上的对应标记点并打开信息窗口
  handleMapLocateAndOpenInfoWindow(base)
}

const handleViewDetails = (base: PracticeBase) => {
  // 优先处理特殊的本地路由
  if (base.id === '3') { // 假设“黄洋界哨口”的ID是'3'，跳转到本地思政课详情页
    router.push(`/political-base/${base.id}`)
  } else if (base.websiteUrl) {
    // 对于所有其他定义了 websiteUrl 的基地，直接在新窗口中打开链接
    window.open(base.websiteUrl, '_blank')
  } else {
    // 如果没有 websiteUrl，可以提供一个反馈或备用逻辑
    console.warn(`基地 "${base.name}" 没有可用的详情页链接。`)
  }
}

const handleBaseLocate = (base: PracticeBase) => {
  // 先确保右侧面板可见
  rightPanelVisible.value = true
  
  // 检查基地是否在当前筛选结果中
  const isBaseVisible = filteredBases.value.some(b => b.id === base.id)
  
  if (!isBaseVisible) {
    // 如果基地不在当前筛选结果中，清除筛选
    searchQuery.value = ''
    selectedCity.value = ''
  }
  
  // 延迟执行滚动，确保面板动画和数据更新完成
  setTimeout(() => {
    const baseElement = document.querySelector(`[data-base-id="${base.id}"]`)
    if (baseElement) {
      baseElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
      
      // 添加高亮效果
      baseElement.classList.add('highlight-base')
      
      // 3秒后移除高亮效果
      setTimeout(() => {
        baseElement.classList.remove('highlight-base')
      }, 3000)
    }
  }, isBaseVisible ? 300 : 500) // 如果需要清除筛选，等待更长时间
}

const handleAdminLogin = () => {
  router.push('/admin/login')
}

const toggleRightPanel = () => {
  rightPanelVisible.value = !rightPanelVisible.value
}

const handleSearch = () => {
  // 搜索逻辑已通过computed实现
}

const handleReset = () => {
  searchQuery.value = ''
  selectedCity.value = ''
  console.log('重置筛选条件')
}

const handleRegionClick = (cityValue: string) => {
  selectedCity.value = cityValue
  console.log('区域点击:', cityValue)
}

// 监听筛选条件变化，自动调整地图视图
watch(
  () => filteredBases.value,
  (newBases) => {
    // 地图视图将由 MapView 内部的 watcher 自动处理
    console.log('筛选后的基地数量变化:', newBases.length)
  },
  { deep: true }
)

// 监听区域选择变化
watch(
  () => selectedCity.value,
  (newCity) => {
    console.log('区域选择变化:', newCity)
    // MapView 将根据 filteredBases 的变化自动更新，无需手动调用
  }
)

// 监听搜索查询变化
watch(
  () => searchQuery.value,
  (newQuery) => {
    console.log('搜索查询变化:', newQuery)
    // MapView 将根据 filteredBases 的变化自动更新，无需手动调用
  }
)

// 监听面板可见性变化
watch(
  () => rightPanelVisible.value,
  (newVisible) => {
    console.log('面板可见性变化:', newVisible)
    // 延迟调整视图以确保面板动画完成
    nextTick(() => {
      setTimeout(() => {
        if (mapViewRef.value && mapViewRef.value.autoFitView) {
          mapViewRef.value.autoFitView()
        }
      }, 400)
    })
  }
)

onMounted(() => {
  loading.value = true
  
  // 初始化移动端检测
  checkMobile()
  
  // 监听窗口大小变化
  window.addEventListener('resize', checkMobile)
  
  // Simulate API call
  setTimeout(() => {
    practiceBaseData.value = mockBases
    loading.value = false
  }, 1000)
})



// 获取每个区域的基地数量
const getRegionCount = (cityValue: string) => {
  if (!cityValue) {
    return practiceBaseData.value.length
  }
  return practiceBaseData.value.filter(base => base.city === cityValue).length
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  if (target) {
    target.style.display = 'none'
  }
}

// 清理函数
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="home-container">
    <!-- Main Content -->
    <div class="main-content">
      <!-- Full Screen Map -->
      <div class="map-section" :class="{ 'map-full-width': !rightPanelVisible }">
        <div v-if="loading" style="position: absolute; inset: 0; display: flex; align-items: center; justify-content: center; background: #f9f9f9; z-index: 50;">
          <div style="text-align: center;">
            <!-- 动画图标 -->
            <div style="position: relative; margin-bottom: 24px;">
              <div style="width: 64px; height: 64px; margin: 0 auto; position: relative;">
                <!-- 旋转外环 -->
                <div style="position: absolute; inset: 0; border: 4px solid rgba(64, 158, 255, 0.3); border-radius: 50%; animation: spin 2s linear infinite;"></div>
                <!-- 脉冲内环 -->
                <div style="position: absolute; inset: 8px; border: 2px solid #003399; border-radius: 50%; animation: pulse 2s infinite;"></div>
                <!-- 中心图标 -->
                <div style="position: absolute; inset: 0; display: flex; align-items: center; justify-content: center;">
                  <Icon icon="material-symbols:location-on" style="width: 32px; height: 32px; color: #003399; animation: bounce 2s infinite;" />
                </div>
              </div>
              <!-- 光圈效果 -->
              <div style="position: absolute; inset: 0; width: 80px; height: 80px; top: -8px; left: -8px; border: 1px solid rgba(64, 158, 255, 0.2); border-radius: 50%; animation: ping 2s infinite;"></div>
            </div>
            
            <!-- 加载文本 -->
            <div>
              <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin-bottom: 8px;">正在加载地图...</h3>
              <p style="font-size: 14px; color: #6b7280; margin-bottom: 16px;">请稍候，正在为您加载井冈山红色教育实践基地地图</p>
              
              <!-- 进度条 -->
              <div style="width: 256px; margin: 0 auto; margin-top: 16px;">
                <div style="height: 8px; background: #e5e7eb; border-radius: 4px; overflow: hidden;">
                  <div style="height: 100%; background: linear-gradient(to right, #003399, #409eff); border-radius: 4px; animation: pulse 2s infinite;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <MapView 
          v-else
          ref="mapViewRef"
          :bases="filteredBases"
          @base-click="handleBaseClick"
          @base-locate="handleBaseLocate"
          @view-details="handleViewDetails"
          :default-center="{ lat: 26.590, lng: 114.165 }" 
          :default-zoom="13" 
          :panel-visible="rightPanelVisible"
          :hide-map-legend="isMobile"
          :hide-map-stats="isMobile"
        />

        <!-- OSS图片 - 地图左上角 -->
        <!-- <div
          class="oss-logo"
          :style="{
            position: 'fixed',
            top: '20px',
            left: '20px',
            zIndex: 1000,
            transition: 'all 0.3s ease'
          }">
          <div style="width: 200px; height: 60px;
                     background: rgba(255, 255, 255, 0.95);
                     border-radius: 12px;
                     border: 1px solid rgba(0, 0, 0, 0.1);
                     display: flex; align-items: center; justify-content: center;
                     backdrop-filter: blur(10px);
                     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                     overflow: hidden;
                     transition: all 0.3s ease;">
            <img
              src="https://hgf2023.oss-cn-shenzhen.aliyuncs.com/dszk/jxnu.png"
              alt="江西师范大学"
              style="width: 85%; height: 85%; object-fit: contain;"
              @error="handleImageError"
            />
          </div>
        </div> -->

        <!-- 精美现代化Header -->
        <div 
          class="header-container"
          :style="{
            position: 'fixed', 
            top: '0', 
            left: rightPanelVisible ? 'calc(50vw - 210px)' : '50vw',
            transform: 'translateX(-50%)',
            zIndex: 1000,
            transition: 'left 0.3s ease'
          }">
          <div style="background: linear-gradient(135deg, 
                        rgba(30, 60, 114, 0.95) 0%, 
                        rgba(42, 82, 152, 0.98) 50%, 
                        rgba(30, 60, 114, 0.95) 100%); 
                      padding: 12px 28px; 
                      border: 1px solid rgba(255, 255, 255, 0.2); 
                      overflow: hidden;
                      max-width: 85vw; 
                      min-height: 44px;
                      transition: all 0.3s ease;
                      position: relative;
                      backdrop-filter: blur(20px) saturate(180%);
                      border-radius: 0 0 32px 32px;
                      box-shadow: 
                        0 6px 24px rgba(0, 0, 0, 0.2), 
                        0 3px 12px rgba(30, 60, 114, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15),
                        inset 0 -1px 0 rgba(0, 0, 0, 0.1);">
            <!-- 优雅光线效果 -->
            <div style="position: absolute; inset: 0; 
                        background: linear-gradient(90deg, 
                          transparent 0%, 
                          rgba(255,255,255,0.08) 30%, 
                          rgba(255,255,255,0.15) 50%, 
                          rgba(255,255,255,0.08) 70%, 
                          transparent 100%); 
                        opacity: 0.7; 
                        animation: gentle-shimmer 6s infinite;
                        border-radius: 0 0 32px 32px;"></div>
            
            <!-- 精致内发光 -->
            <div style="position: absolute; inset: 1px; 
                        background: linear-gradient(135deg, 
                          rgba(255,255,255,0.1) 0%, 
                          transparent 30%, 
                          transparent 70%, 
                          rgba(255,255,255,0.05) 100%);
                        border-radius: 0 0 32px 32px;
                        pointer-events: none;"></div>
            
            <!-- 外发光光晕 -->
            <div style="position: absolute; inset: -8px; 
                        background: radial-gradient(ellipse at center, 
                          rgba(30, 60, 114, 0.4) 0%, 
                          rgba(42, 82, 152, 0.2) 50%, 
                          transparent 80%);
                        filter: blur(16px);
                        pointer-events: none;
                        z-index: -1;
                        border-radius: 0 0 40px 40px;
                        animation: soft-pulse 4s infinite ease-in-out;"></div>
            
                          <!-- 自适应内容区域 -->
              <div style="position: relative; display: flex; align-items: center; justify-content: center;
                         min-height: 44px; padding: 6px 0; gap: 10px; flex-wrap: wrap;">


                <!-- 简洁图标 -->
                <div style="position: relative; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <!-- 图标容器 -->
                  <div style="width: 32px; height: 32px;
                             background: rgba(255,255,255,0.15);
                             border-radius: 50%;
                             border: 2px solid rgba(255,255,255,0.4);
                             display: flex; align-items: center; justify-content: center;
                             backdrop-filter: blur(4px);
                             box-shadow: 0 3px 8px rgba(0,0,0,0.15);
                             animation: icon-float 3s infinite ease-in-out;">
                    <Icon icon="material-symbols:location-on"
                          style="width: 18px; height: 18px; color: #ffffff;
                                filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));" />
                  </div>
                </div>
                
                <!-- 自适应标题 -->
                <div style="position: relative; flex: 1; min-width: 0; text-align: center;">
                  <h1 style="color: #ffffff; 
                            font-size: clamp(14px, 3.5vw, 20px);
                            font-weight: 500; 
                            letter-spacing: 0.8px;
                            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3)); 
                            line-height: 1.2; 
                            margin: 0;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
                            -webkit-background-clip: text;
                            background-clip: text;
                            font-family: 'KaiTi', '楷体', 'SimKai', 'STKaiti', 'FangSong', '仿宋', 'SimSun', '宋体', serif;">
                    江西省学校"大思政课"实践教学基地数字地图
                  </h1>
                  
                  <!-- 精致装饰线 -->
                  <div style="position: absolute; bottom: -1px; left: 50%; transform: translateX(-50%); 
                             width: 75%; height: 1px; 
                             background: linear-gradient(90deg, 
                               transparent 0%, 
                               rgba(255,255,255,0.4) 30%, 
                               rgba(255,255,255,0.7) 50%, 
                               rgba(255,255,255,0.4) 70%, 
                               transparent 100%);
                             opacity: 0.8;"></div>
                </div>
              </div>
          </div>
        </div>
        
        <!-- 右上角控制按钮组 - 仅桌面端显示 -->
        <div 
          v-show="!isMobile"
          class="control-buttons"
          :style="{
            position: 'fixed', 
            top: '20px', 
            right: rightPanelVisible ? '431px' : '11px',
            zIndex: 1001, 
            display: 'flex', 
            flexDirection: 'column', 
            gap: '12px',
            transition: 'right 0.3s ease'
          }">
          <!-- 管理员入口 -->
          <el-tooltip content="系统管理" placement="left">
            <button @click="handleAdminLogin" 
                    class="modern-btn modern-btn-secondary">
              <Icon icon="material-symbols:settings" 
                    style="width: 24px; height: 24px; transition: transform 0.3s ease;" />
            </button>
          </el-tooltip>
          
          <!-- 侧边栏控制按钮 -->
          <el-tooltip :content="rightPanelVisible ? '收起面板' : '展开面板'" placement="left">
            <button @click="toggleRightPanel" 
                    class="modern-btn modern-btn-primary">
              <Icon :icon="rightPanelVisible ? 'material-symbols:chevron-right' : 'material-symbols:chevron-left'" 
                    style="width: 24px; height: 24px; transition: transform 0.3s ease;" />
            </button>
          </el-tooltip>
        </div>
       </div>

      <!-- Right Panel - 仅桌面端显示 -->
      <Transition name="slide-panel">
        <div v-show="rightPanelVisible && !isMobile" class="right-panel">
          <div class="panel-header">
            <h3>区域选择</h3>
            <el-button @click="toggleRightPanel" text>
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>

          <!-- Search Section -->
          <div class="search-section">
            <el-input
              v-model="searchQuery"
              placeholder="请输入基地名称或关键词搜索"
              :prefix-icon="Search"
              @keyup.enter="handleSearch"
              clearable
              size="large"
            />
          </div>

          <!-- 区域选择Section -->
          <div class="region-selection-section">
            <div class="region-header">
              <Icon icon="material-symbols:map" style="width: 18px; height: 18px; color: #409eff;" />
              <span class="region-title">选择区域</span>
            </div>
            
            <div class="region-grid">
              <div 
                v-for="city in cities" 
                :key="city.value"
                @click="handleRegionClick(city.value)"
                class="region-item"
                :class="{ 'active': selectedCity === city.value }"
              >
                <span class="region-label">{{ city.label }}</span>
              </div>
            </div>
          </div>

          <!-- Results Count -->
          <div class="results-count">
            <span>共找到 <strong>{{ filteredBases.length }}</strong> 个基地</span>
            <el-button 
              v-if="searchQuery || selectedCity" 
              @click="handleReset" 
              type="primary" 
              text 
              size="small">
              <Icon icon="material-symbols:refresh" style="width: 14px; height: 14px; margin-right: 4px;" />
              重置
            </el-button>
          </div>

          <!-- Base List -->
          <div class="base-list-section">
            <div class="section-header">
              <h4>
                <el-icon><List /></el-icon>
                基地列表
              </h4>
            </div>
            
            <div class="base-list">
              <div 
                v-for="base in filteredBases" 
                :key="base.id"
                :data-base-id="base.id"
                class="base-item"
                @click="handleBaseClick(base)"
                :title="`点击定位到地图上的 ${base.name}`"
              >
                <div class="base-image">
                  <img :src="base.imageUrl" :alt="base.name" />
                  <div class="base-category">{{ base.category }}</div>
                </div>
                <div class="base-info">
                  <h5 class="base-name">{{ base.name }}</h5>
                  <p class="base-location">
                    <el-icon><Location /></el-icon>
                    {{ base.city }}
                  </p>
                  <p class="base-description">{{ base.description.substring(0, 60) }}...</p>
                  <div class="base-rating">
                    <el-rate 
                      v-model="base.rating" 
                      disabled 
                      size="small"
                      text-color="#ff9900"
                    />
                    <span class="rating-text">{{ base.rating }}</span>
                  </div>

                  <!-- 所有基地的操作按钮 -->
                  <div class="base-actions">
                    <a 
                      v-if="base.websiteUrl"
                      @click.stop="handleViewDetails(base)" 
                      class="detail-link"
                    >
                      查看详情
                    </a>
                    <span v-if="base.websiteUrl" class="link-separator">|</span>
                    <span class="location-hint">点击卡片定位到地图</span>
                  </div>

                </div>
              </div>
              
              <div v-if="filteredBases.length === 0" class="empty-state">
                <el-empty description="没有找到符合条件的基地" :image-size="80" />
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 移动端底部UI -->
    <div v-if="isMobile" class="mobile-ui">
      <!-- 底部搜索栏 -->
      <div class="mobile-search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="请输入要查找的实践基地名称"
          :prefix-icon="Search"
          size="large"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-button 
              v-if="searchQuery" 
              @click="handleSearch" 
              type="primary" 
              text 
              size="small"
              style="margin-right: 8px;"
            >
              搜索
            </el-button>
          </template>
        </el-input>
      </div>

      <!-- 底部区域选择 - 横向滚动 -->
      <div class="mobile-region-selector">
        <div class="mobile-region-scroll">
          <div 
            v-for="city in cities" 
            :key="city.value"
            @click="handleRegionClick(city.value)"
            class="mobile-region-card"
            :class="{ 'active': selectedCity === city.value }"
          >
            <div class="region-card-content">
              <div class="region-name">{{ city.label }}</div>
              <div class="region-count">{{ getRegionCount(city.value) }}个</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 结果统计 -->
      <div class="mobile-results-count">
        <span>共找到 <strong>{{ filteredBases.length }}</strong> 个基地</span>
        <el-button 
          v-if="searchQuery || selectedCity" 
          @click="handleReset" 
          type="primary" 
          text 
          size="small">
          <Icon icon="material-symbols:refresh" style="width: 12px; height: 12px; margin-right: 4px;" />
          重置
        </el-button>
      </div>

      <!-- 直接展示基地列表 -->
      <div class="mobile-base-list-container">
        <div class="mobile-base-list">
          <!-- 加载状态 -->
          <div v-if="loading" class="mobile-loading-state">
            <Icon icon="material-symbols:refresh" class="loading-icon" style="width: 32px; height: 32px;" />
            <p>正在加载基地信息...</p>
          </div>
          
          <!-- 基地列表 -->
          <div 
            v-else
            v-for="base in filteredBases" 
            :key="base.id"
            :data-base-id="base.id"
            class="mobile-base-item"
            @click="handleBaseClick(base)"
          >
            <div class="mobile-base-image-wrapper">
              <div class="mobile-base-image">
                <img :src="base.imageUrl" :alt="base.name" />
                <div class="mobile-base-category">{{ base.category }}</div>
              </div>
            </div>
            <div class="mobile-base-info">
              <h5 class="mobile-base-name">{{ base.name }}</h5>
              <p class="mobile-base-location">
                <el-icon><Location /></el-icon>
                {{ base.city }}
              </p>
              <p class="mobile-base-description">{{ base.description.substring(0, 60) }}...</p>
              <div class="mobile-base-actions">
                <el-button 
                  @click.stop="handleViewDetails(base)" 
                  type="primary" 
                  size="small"
                  plain>
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="!loading && filteredBases.length === 0" class="mobile-empty-state">
            <el-empty description="没有找到符合条件的基地" :image-size="60" />
          </div>
          
          <!-- 滚动提示 -->
          <div v-if="!loading && filteredBases.length > 3" class="mobile-scroll-hint">
            <Icon icon="material-symbols:keyboard-arrow-up" style="width: 16px; height: 16px; opacity: 0.6;" />
            <span>向上滑动查看更多基地</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.map-section {
  flex: 1;
  position: relative;
  transition: all 0.3s ease;
}

.map-full-width {
  width: 100%;
}

/* Header and button styles */

/* 现代化按钮样式 */
.modern-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
}

.modern-btn-secondary {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 51, 153, 0.3);
  backdrop-filter: blur(10px);
  color: #003399;
}

.modern-btn-secondary:hover {
  background: #003399;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 51, 153, 0.3);
}

.modern-btn-primary {
  background: #409eff;
  color: white;
}

.modern-btn-primary:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

/* 动画关键帧 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 移除旧的控制器样式 */
.map-controls-wrapper {
  display: none;
}

/* 移除旧的header样式 */
.header-overlay, .title-with-icon {
  display: none;
}

.right-panel {
  width: 420px;
  background: white;
  border-left: 1px solid #e0e6ed;
  display: flex;
  flex-direction: column;
  z-index: 999;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.search-section {
  padding: 24px 28px;
  border-bottom: 1px solid #ebeef5;
  background: #fafbfc;
}

/* 区域选择样式 */
.region-selection-section {
  padding: 24px 28px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;
}

.region-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}

.region-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.region-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  min-height: 36px;
  position: relative;
  overflow: hidden;
}

.region-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.region-item:hover::before {
  left: 100%;
}

.region-item:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f3ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.15);
}

.region-item.active {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

.region-item.active .region-icon {
  color: white;
}

.region-label {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  transition: all 0.3s ease;
}

.results-count {
  padding: 18px 28px;
  background: linear-gradient(135deg, #f0f4f8 0%, #e6f3ff 100%);
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-count strong {
  color: #409eff;
  font-weight: 700;
}

.base-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  padding: 20px 28px;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.base-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

.base-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f2f5;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.base-item::before {
  /* content: '📍 点击定位'; */
  position: absolute;
  top: 50%;
  right: -100px;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #409eff, #66b3ff);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.base-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: translateX(4px);
  box-shadow: 4px 0 12px rgba(64, 158, 255, 0.1);
}

.base-item:hover::before {
  right: 16px;
  opacity: 1;
}

.base-item:last-child {
  border-bottom: none;
}

/* 高亮效果 */
.base-item.highlight-base {
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%) !important;
  border: 2px solid #409eff !important;
  transform: translateX(8px) !important;
  box-shadow: 8px 0 24px rgba(64, 158, 255, 0.3) !important;
  animation: highlight-pulse 0.6s ease-out;
}

@keyframes highlight-pulse {
  0% {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    transform: translateX(12px) scale(1.02);
    box-shadow: 12px 0 32px rgba(64, 158, 255, 0.5);
  }
  50% {
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
    transform: translateX(8px) scale(1.01);
    box-shadow: 8px 0 24px rgba(64, 158, 255, 0.3);
  }
  100% {
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
    transform: translateX(8px);
    box-shadow: 8px 0 24px rgba(64, 158, 255, 0.3);
  }
}

.base-image {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 0 0 35%;
  height: 115px;
}

.base-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.base-item:hover .base-image img {
  transform: scale(1.05);
}

.base-category {
  position: absolute;
  top: 4px;
  left: 4px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(30, 60, 114, 0.9));
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

.base-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.base-name {
  margin: 0 0 6px 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  max-width: 100%;
  word-break: break-all;
}

.base-location {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.base-description {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  max-width: 100%;
}

.base-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-text {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.base-actions {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.base-actions .el-button {
  font-size: 12px;
  padding: 4px 12px;
}

.detail-link {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 6px;
  cursor: pointer;
}

.detail-link:hover {
  color: #1e40af;
  background: rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.link-separator {
  color: #e5e7eb;
  font-weight: 300;
}

.location-hint {
  color: #6b7280;
  font-size: 11px;
  font-weight: 400;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* Panel slide transition */
.slide-panel-enter-active,
.slide-panel-leave-active {
  transition: all 0.3s ease;
}

.slide-panel-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-panel-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  /* 移动端整体布局 */
  .home-container {
    padding-bottom: 0;
  }
  
  .main-content {
    padding-bottom: 0;
  }
  
  /* 为地图腾出底部空间 */
  .map-section {
    padding-bottom: 45vh;
  }
  
  /* 现代Header移动端适配 */
  .home-container [style*="max-width: 85vw"] {
    max-width: 90vw !important;
    min-height: 38px !important;
    padding: 10px 20px !important;
    border-radius: 0 0 24px 24px !important;
  }
  
  .home-container [style*="font-size: clamp(14px, 3.5vw, 20px)"] {
    font-size: clamp(12px, 3vw, 16px) !important;
    letter-spacing: 0.5px !important;
  }
  
  /* OSS图片移动端适配 */
  .oss-logo {
    top: 10px !important;
    left: 10px !important;
  }

  .oss-logo [style*="width: 60px; height: 60px"] {
    width: 45px !important;
    height: 45px !important;
    border-radius: 8px !important;
  }

  .home-container [style*="width: 32px; height: 32px"] {
    width: 24px !important;
    height: 24px !important;
  }

  .home-container [style*="width: 18px; height: 18px"] {
    width: 14px !important;
    height: 14px !important;
  }
  
  .home-container [style*="gap: 10px"] {
    gap: 6px !important;
  }
  
  /* 移动端header适配 - 始终居中 */
  .home-container .header-container {
    left: 50vw !important;
    transform: translateX(-50%) !important;
  }
  
  /* 移动端现代边框调整 */
  .home-container [style*="border-radius: 0 0 32px 32px"] {
    border-radius: 0 0 24px 24px !important;
  }
  
  /* 移动端优雅hover效果 */
  .home-container [style*="border-radius: 0 0 32px 32px"]:hover {
    transform: translateX(-50%) translateY(-1px) scale(1.005) !important;
    box-shadow: 
      0 6px 20px rgba(0, 0, 0, 0.3),
      0 3px 10px rgba(30, 60, 114, 0.35) !important;
    filter: brightness(1.03) !important;
  }
  
  /* 移动端触摸反馈 */
  .home-container [style*="border-radius: 0 0 32px 32px"]:active {
    transform: translateX(-50%) translateY(-0.5px) scale(0.995) !important;
    box-shadow: 
      0 3px 12px rgba(0, 0, 0, 0.25),
      0 2px 6px rgba(30, 60, 114, 0.3) !important;
  }

  /* 隐藏桌面端的右侧面板相关元素 */
  .right-panel {
    display: none !important;
  }

  /* 确保地图在移动端占据全宽 */
  .map-section {
    width: 100% !important;
  }
  
  /* 移动端底部UI的安全区域 */
  .mobile-ui {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* 防止过度滚动 */
  body {
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }
}

/* OSS Logo 样式 */
.oss-logo:hover [style*="width: 60px; height: 60px"] {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.oss-logo [style*="width: 60px; height: 60px"]:active {
  transform: scale(0.95);
}

/* Custom scrollbar for base list */
.base-list::-webkit-scrollbar {
  width: 6px;
}

.base-list::-webkit-scrollbar-track {
  background: #f1f3f4;
}

.base-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #c0c4cc, #909399);
  border-radius: 3px;
}

.base-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #909399, #606266);
}

/* Enhanced visual effects */
.header-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 现代Header悬停效果 */
.home-container [style*="border-radius: 0 0 32px 32px"] {
  transition: all 0.3s ease !important;
  cursor: pointer;
  position: relative;
}

.home-container [style*="border-radius: 0 0 32px 32px"]:hover {
  transform: translateY(-2px) scale(1.005);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.25),
    0 5px 16px rgba(30, 60, 114, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15) !important;
  filter: brightness(1.05) saturate(1.05);
}

/* Header精致光效 */
.home-container [style*="border-radius: 0 0 32px 32px"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.12) 0%, 
    rgba(255,255,255,0.06) 40%, 
    rgba(255,255,255,0.02) 70%, 
    rgba(0,0,0,0.03) 100%);
  border-radius: 0 0 32px 32px;
  pointer-events: none;
  animation: subtle-shimmer 5s infinite;
}

/* 现代动画集合 */
@keyframes subtle-shimmer {
  0% { 
    opacity: 0.8;
    background: linear-gradient(135deg, 
      rgba(255,255,255,0.12) 0%, 
      rgba(255,255,255,0.06) 40%, 
      rgba(255,255,255,0.02) 70%, 
      rgba(0,0,0,0.03) 100%);
  }
  50% { 
    opacity: 1;
    background: linear-gradient(135deg, 
      rgba(255,255,255,0.16) 0%, 
      rgba(255,255,255,0.09) 40%, 
      rgba(255,255,255,0.04) 70%, 
      rgba(0,0,0,0.01) 100%);
  }
  100% { 
    opacity: 0.8;
    background: linear-gradient(135deg, 
      rgba(255,255,255,0.12) 0%, 
      rgba(255,255,255,0.06) 40%, 
      rgba(255,255,255,0.02) 70%, 
      rgba(0,0,0,0.03) 100%);
  }
}

@keyframes gentle-shimmer {
  0% { 
    opacity: 0.6;
    transform: translateX(-10px);
  }
  50% { 
    opacity: 0.8;
    transform: translateX(10px);
  }
  100% { 
    opacity: 0.6;
    transform: translateX(-10px);
  }
}

@keyframes soft-pulse {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(1);
  }
  50% { 
    opacity: 0.5;
    transform: scale(1.02);
  }
}

@keyframes icon-float {
  0%, 100% { 
    transform: translateY(0);
  }
  50% { 
    transform: translateY(-2px);
  }
}

.right-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, #409eff, #1e3c72);
  z-index: 1;
}

/* 移动端样式 */
.mobile-ui {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid #e0e6ed;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  height: 45vh;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  border-radius: 20px 20px 0 0;
}

.mobile-search-bar {
  padding: 10px 16px 6px;
  border-bottom: 1px solid #f0f2f5;
  flex-shrink: 0;
}

.mobile-region-selector {
  padding: 10px 16px;
  border-bottom: 1px solid #f0f2f5;
  flex-shrink: 0;
}

.mobile-region-scroll {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 4px 0;
  scroll-behavior: smooth;
}

.mobile-region-scroll::-webkit-scrollbar {
  height: 4px;
}

.mobile-region-scroll::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 2px;
}

.mobile-region-scroll::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}

.mobile-region-scroll::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

.mobile-region-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  min-width: 80px;
  text-align: center;
}

.mobile-region-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.mobile-region-card:hover::before {
  left: 100%;
}

.mobile-region-card:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f3ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.mobile-region-card.active {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

.region-card-content {
  position: relative;
  z-index: 1;
}

.region-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.region-count {
  font-size: 11px;
  opacity: 0.8;
  text-align: center;
  white-space: nowrap;
}

.mobile-region-card.active .region-count {
  opacity: 0.9;
}

.mobile-results-count {
  padding: 8px 16px;
  background: linear-gradient(135deg, #f0f4f8 0%, #e6f3ff 100%);
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.mobile-results-count strong {
  color: #409eff;
  font-weight: 700;
}

.mobile-base-list-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.mobile-base-list-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 15px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
  z-index: 1;
}

.mobile-base-list-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 15px;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
  z-index: 1;
}

.mobile-base-list {
  flex: 1;
  overflow-y: auto;
  padding: 6px 0 16px 0;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  min-height: 150px;
}

.mobile-base-list::-webkit-scrollbar {
  width: 4px;
}

.mobile-base-list::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 2px;
}

.mobile-base-list::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 2px;
}

.mobile-base-list::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

.mobile-base-item {
  display: flex;
  padding: 16px 18px;
  border-bottom: 1px solid #f0f2f5;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: white;
  border-radius: 10px;
  margin: 4px 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  animation: mobile-item-fade-in 0.4s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
  min-height: 140px;
}

@keyframes mobile-item-fade-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-base-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mobile-base-item:active {
  transform: translateX(2px) scale(0.98);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.mobile-base-item:last-child {
  border-bottom: none;
}

/* 移动端高亮效果 */
.mobile-base-item.highlight-base {
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%) !important;
  border-left: 4px solid #409eff !important;
  transform: translateX(8px) !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3) !important;
  animation: mobile-highlight-pulse 0.6s ease-out;
}

@keyframes mobile-highlight-pulse {
  0% {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    transform: translateX(12px) scale(1.02);
    box-shadow: 0 6px 24px rgba(64, 158, 255, 0.5);
  }
  50% {
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
    transform: translateX(8px) scale(1.01);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  }
  100% {
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
    transform: translateX(8px);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  }
}

.mobile-base-image-wrapper {
  flex: 0 0 35%;
  height: 120px;
  margin-right: 16px;
}

.mobile-base-image {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

.mobile-base-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.mobile-base-item:hover .mobile-base-image img {
  transform: scale(1.05);
}

.mobile-base-item:hover .mobile-base-image {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.mobile-base-category {
  position: absolute;
  top: 6px;
  left: 6px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(30, 60, 114, 0.9));
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.mobile-base-info {
  flex: 1;
  min-width: 0;
}

.mobile-base-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mobile-base-location {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.mobile-base-description {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.mobile-base-actions {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-base-actions .el-button {
  font-size: 12px;
  padding: 4px 12px;
}



.mobile-empty-state {
  padding: 40px 20px;
  text-align: center;
}

.mobile-loading-state {
  padding: 40px 20px;
  text-align: center;
  color: #909399;
}

.mobile-loading-state .loading-icon {
  margin-bottom: 12px;
  color: #409eff;
  animation: spin 1s linear infinite;
}

.mobile-scroll-hint {
  text-align: center;
  padding: 12px 20px;
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  opacity: 0.8;
  border-top: 1px solid #f0f2f5;
  margin-top: 8px;
}



/* 移动端特定的响应式调整 */
@media (max-width: 480px) {
  .mobile-ui {
    height: 50vh;
  }
  
  .map-section {
    padding-bottom: 50vh !important;
  }
  
  .mobile-region-card {
    padding: 6px 10px;
    min-width: 70px;
  }
  
  .region-name {
    font-size: 13px;
  }
  
  .region-count {
    font-size: 10px;
  }
  
  .mobile-base-image-wrapper {
    flex: 0 0 38%;
    height: 120px;
  }
  
  .mobile-base-name {
    font-size: 15px;
  }
  
  .mobile-base-location,
  .mobile-base-description {
    font-size: 12px;
  }
  
  .mobile-base-item {
    padding: 14px 16px;
    min-height: 120px;
  }
  
  .mobile-search-bar {
    padding: 8px 12px 4px;
  }
  
  .mobile-region-selector {
    padding: 8px 12px;
  }
  
  .mobile-results-count {
    padding: 6px 12px;
    font-size: 13px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 375px) {
  .mobile-ui {
    height: 55vh;
  }
  
  .map-section {
    padding-bottom: 55vh !important;
  }
  
  .mobile-region-card {
    padding: 4px 8px;
    min-width: 60px;
  }
  
  .region-name {
    font-size: 12px;
  }
  
  .region-count {
    font-size: 9px;
  }
  
  .mobile-base-image-wrapper {
    flex: 0 0 35%;
    height: 90px;
  }
  
  .mobile-base-name {
    font-size: 14px;
  }
  
  .mobile-base-location,
  .mobile-base-description {
    font-size: 11px;
  }
  
  .mobile-base-item {
    padding: 12px 14px;
    min-height: 110px;
  }
}

/* 平板适配 */
@media (min-width: 481px) and (max-width: 768px) {
  .mobile-ui {
    height: 40vh;
    padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
  }
  
  .map-section {
    padding-bottom: 40vh !important;
  }
  
  .mobile-region-card {
    min-width: 90px;
  }
  
  .mobile-base-image-wrapper {
    flex: 0 0 42%;
    height: 130px;
  }
}
</style>