# CORS问题解决方案

## 问题描述
您的应用在开发环境中遇到了CORS（跨域资源共享）错误，无法从 `http://localhost:5174` 向 `http://*************:8100` 发送请求。

## 解决方案

### 1. 开发环境解决方案（推荐）

我们已经在 `vite.config.ts` 中配置了代理，这样您就可以在开发环境中正常访问API了。

**配置说明：**
- 所有以 `/api` 开头的请求都会被代理到 `http://*************:8100`
- 代理会自动移除 `/api` 前缀
- 例如：`/api/category/public/categoryList` → `http://*************:8100/category/public/categoryList`

### 2. 环境变量配置

在项目根目录创建 `.env.development` 文件（如果不存在）：

```bash
# 开发环境配置
VITE_API_BASE_URL=/api

# 百度地图API密钥（请替换为您的实际密钥）
VITE_BAIDU_MAP_AK=your_baidu_map_ak_here

# 天地图API密钥（请替换为您的实际密钥）
VITE_TIANDITU_MAP_TK=your_tianditu_map_tk_here
```

### 3. 重启开发服务器

配置完成后，请重启您的开发服务器：

```bash
npm run dev
# 或
yarn dev
```

### 4. 验证解决方案

重启后，您的API请求应该能够正常工作。您可以在浏览器控制台中看到：
- 请求URL变为：`http://localhost:5174/api/category/public/categoryList`
- 不再出现CORS错误
- 网络请求状态显示为成功

## 生产环境配置

对于生产环境，您需要：

1. 确保后端服务器配置了正确的CORS头
2. 或者使用相同的代理配置
3. 在部署时设置正确的 `VITE_API_BASE_URL` 环境变量

## 后端CORS配置（可选）

如果您有权限修改后端服务器，可以在后端添加以下CORS头：

```javascript
// Express.js 示例
app.use(cors({
  origin: ['http://localhost:5174', 'http://localhost:3000'],
  credentials: true
}));
```

## 故障排除

如果问题仍然存在：

1. 检查 `vite.config.ts` 中的代理配置是否正确
2. 确认后端服务器 `http://*************:8100` 是否正常运行
3. 检查网络连接是否正常
4. 查看浏览器控制台是否有其他错误信息

## 注意事项

- 代理配置仅在开发环境中有效
- 生产环境需要后端正确配置CORS或使用其他解决方案
- 确保环境变量文件没有被版本控制系统忽略
