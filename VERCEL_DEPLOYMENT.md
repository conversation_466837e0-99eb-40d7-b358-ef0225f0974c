# Vercel部署指南

本文档提供在Vercel上部署使用天地图的Vue应用的详细步骤。

## 前置准备

1. 有效的天地图API密钥（从[天地图控制台](https://console.tianditu.gov.cn/)获取）
2. 有效的百度地图API密钥（从[百度地图控制台](https://lbsyun.baidu.com/apiconsole/key)获取）
3. Vercel账号

## 部署步骤

### 1. 构建项目

确保项目能正常构建：

```bash
npm run build
```

### 2. 创建Vercel项目

1. 登录[Vercel控制台](https://vercel.com/)
2. 点击"New Project"
3. 导入您的Git仓库
4. 配置项目设置：
   - **Framework Preset**: Vue.js
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`

### 3. 配置环境变量

在Vercel项目设置中添加以下环境变量：

1. `VITE_TIANDITU_MAP_TK` - 您的天地图API密钥
2. `VITE_BAIDU_MAP_AK` - 您的百度地图API密钥

![环境变量设置](https://vercel.com/docs/concepts/projects/environment-variables)

### 4. 配置API密钥域名限制

1. 登录[天地图控制台](https://console.tianditu.gov.cn/)
2. 找到您的API密钥
3. 添加您的Vercel部署域名到白名单：
   - `your-project.vercel.app`
   - 您的自定义域名（如果有）

### 5. 部署项目

点击"Deploy"按钮部署项目。

## 验证部署

部署完成后，访问您的Vercel应用URL，确认：

1. 地图能正常加载
2. 标记点能正常显示
3. 导航功能能正常工作

## 常见问题排查

### 地图无法加载

可能的原因：

1. **环境变量未正确设置**
   - 检查Vercel环境变量是否正确配置
   - 确认变量名称为`VITE_TIANDITU_MAP_TK`和`VITE_BAIDU_MAP_AK`

2. **API密钥域名限制**
   - 确保天地图API密钥白名单包含您的Vercel域名

3. **CSP限制**
   - 检查`vercel.json`文件中的CSP配置是否正确

### 导航功能不工作

1. **百度地图API密钥问题**
   - 确认百度地图API密钥正确配置
   - 检查百度地图API密钥的域名白名单

2. **移动端兼容性**
   - 在不同设备上测试导航功能

## 性能优化建议

1. **启用Vercel Edge缓存**
   - 在`vercel.json`中配置缓存规则

2. **使用Vercel Image Optimization**
   - 优化图片加载性能

3. **配置预加载**
   - 使用`<link rel="preconnect">`预连接到天地图域名

## 更新部署

推送新代码到Git仓库后，Vercel会自动重新部署。

如果修改了环境变量，需要在Vercel控制台中手动更新。 